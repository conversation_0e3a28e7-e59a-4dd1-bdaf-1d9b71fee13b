.publish-container { background: #f5f6fa; min-height: 100vh; padding-bottom: 40rpx; }
.publish-header { display: flex; align-items: center; justify-content: center; position: relative; padding: 0 32rpx; height: 100rpx; background: #fff; border-bottom: 1rpx solid #f0f0f0; }
.publish-title { font-size: 38rpx; font-weight: bold; color: #222; flex: 1; text-align: center; }
.publish-btn { position: absolute; right: 16rpx; top: 50%; transform: translateY(-50%); display: flex; align-items: center; justify-content: center; background: none; border: none; box-shadow: none; }
.publish-btn-icon { width: 38rpx; height: 38rpx; margin-right: 0; }
.publish-btn-text { color: #2176ff; font-size: 32rpx; margin-left: 8rpx; display: inline; }
.publish-section { background: #fff; border-radius: 24rpx; margin: 32rpx 24rpx 0 24rpx; padding: 32rpx 24rpx; box-shadow: 0 2rpx 8rpx #e5e7eb22; }
.publish-label-row { display: flex; align-items: center; justify-content: space-between; margin-bottom: 18rpx; }
.publish-label { font-size: 32rpx; color: #222; font-weight: 500; }
.publish-tip { font-size: 24rpx; color: #888; }
.publish-img-list { display: flex; align-items: center; gap: 18rpx; flex-wrap: wrap; }
.publish-img-upload { width: 140rpx; height: 140rpx; background: #f3f4f6; border-radius: 18rpx; display: flex; align-items: center; justify-content: center; font-size: 80rpx; color: #bbb; border: 2rpx dashed #d1d5db; cursor: pointer; }
.publish-img-plus { font-size: 80rpx; color: #bbb; }
.publish-img-thumb { width: 140rpx; height: 140rpx; border-radius: 18rpx; object-fit: cover; }
.publish-section-title { font-size: 32rpx; color: #222; font-weight: bold; margin-bottom: 18rpx; }
.publish-input { width: 100%; height: 80rpx; border-radius: 16rpx; border: 1.5rpx solid #e5e7eb; background: #f7faff; font-size: 30rpx; color: #222; padding: 0 24rpx; margin-bottom: 22rpx; }
.publish-picker-text { color: #888; font-size: 30rpx; line-height: 80rpx; }
.publish-category-list { display: flex; flex-wrap: wrap; gap: 18rpx; margin-top: 12rpx; }
.publish-category-item { padding: 18rpx 38rpx; border-radius: 16rpx; background: #f3f4f6; color: #2176ff; font-size: 30rpx; border: 2rpx solid #e5e7eb; }
.publish-category-item.active { background: #e6f0ff; border-color: #2176ff; color: #2176ff; font-weight: bold; }
.publish-textarea { width: 100%; min-height: 120rpx; border-radius: 16rpx; border: 1.5rpx solid #e5e7eb; background: #f7faff; font-size: 30rpx; color: #222; padding: 18rpx 24rpx; margin-top: 18rpx; resize: none; }
.publish-delivery-list { display: flex; gap: 18rpx; margin: 18rpx 0 0 0; }
.publish-delivery-item { padding: 16rpx 36rpx; border-radius: 16rpx; background: #f3f4f6; color: #2176ff; font-size: 28rpx; border: 2rpx solid #e5e7eb; }
.publish-delivery-item.active { background: #e6f0ff; border-color: #2176ff; color: #2176ff; font-weight: bold; }

.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.form-group {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.upload-box {
  width: 100%;
  height: 300rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  font-size: 28rpx;
}

.upload-placeholder .iconfont {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.picker {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.textarea {
  width: 100%;
  height: 200rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #07c160;
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn.disabled {
  background: #9be0b8;
} 