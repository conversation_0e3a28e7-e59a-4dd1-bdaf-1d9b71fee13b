const API = require('../../utils/api')

Page({
  data: {
    user: null,
    loading: true,
    activeTabbarIndex: 4,
    showEditModal: false,
    editUser: {},
    genderOptions: ['男', '女', '保密'],
    editGenderIndex: 0,

    // 书籍相关数据
    onsaleBooks: [],
    soldBooks: [],
    favBooks: [],
    historyBooks: [],

    // 模态框状态
    showOnsaleList: false,
    showSoldList: false,
    showFavList: false,
    showHistoryList: false,
    showEditBookModal: false,
    showConfirmModal: false,

    // 编辑相关
    editBook: {},
    confirmType: '',
    confirmBookId: null,

    // 统计数据
    stats: {
      onsaleCount: 0,
      soldCount: 0,
      favCount: 0,
      historyCount: 0
    }
  },

  onLoad() {
    this.loadUserData();
  },

  onShow() {
    this.loadUserData();
  },

  async loadUserData() {
    try {
      this.setData({ loading: true });

      // 获取用户信息
      const userResult = await API.getUserInfo();
      if (userResult.success) {
        this.setData({ user: userResult.data });
      }

      // 并行加载各种数据
      await Promise.all([
        this.loadOnsaleBooks(),
        this.loadSoldBooks(),
        this.loadFavorites(),
        this.loadViewHistory()
      ]);

    } catch (error) {
      console.error('加载用户数据失败:', error);
      API.showError('加载数据失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  async loadOnsaleBooks() {
    try {
      const result = await API.getBookList({
        sellerId: this.data.user?._id,
        status: 'on_sale',
        page: 1,
        pageSize: 10
      });
      if (result.success) {
        this.setData({
          onsaleBooks: result.data.list,
          'stats.onsaleCount': result.data.total || result.data.list.length
        });
      }
    } catch (error) {
      console.error('获取在售书籍失败:', error);
    }
  },

  async loadSoldBooks() {
    try {
      const result = await API.call('user', 'getSoldBooks');
      if (result.success) {
        this.setData({
          soldBooks: result.data,
          'stats.soldCount': result.data.length
        });
      }
    } catch (error) {
      console.error('获取已售书籍失败:', error);
    }
  },

  async loadFavorites() {
    try {
      const result = await API.getFavorites();
      if (result.success) {
        this.setData({
          favBooks: result.data,
          'stats.favCount': result.data.length
        });
      }
    } catch (error) {
      console.error('获取收藏列表失败:', error);
    }
  },

  async loadViewHistory() {
    try {
      const result = await API.getViewHistory();
      if (result.success) {
        this.setData({
          historyBooks: result.data,
          'stats.historyCount': result.data.length
        });
      }
    } catch (error) {
      console.error('获取浏览记录失败:', error);
    }
  },
  // 导航相关
  onTabbarTap(e) {
    const index = Number(e.currentTarget.dataset.index);
    if (index === this.data.activeTabbarIndex) return;
    this.setData({ activeTabbarIndex: index });

    if (index === 0) {
      wx.redirectTo({ url: '/pages/index/index' });
    } else if (index === 1) {
      wx.redirectTo({ url: '/pages/discover/discover' });
    } else if (index === 3) {
      wx.navigateTo({ url: '/pages/message/message' });
    }
  },
  onTabbarPublish() {
    wx.navigateTo({ url: '/pages/publish/publish' });
  },
  onTabbarMessage() {
    this.setData({ activeTabbarIndex: 3 });
    wx.redirectTo({ url: '/pages/message/message' });
  },
  onTabbarDiscover() {
    this.setData({ activeTabbarIndex: 1 });
    wx.redirectTo({ url: '/pages/discover/discover' });
  },
  // 显示在售书籍列表
  onShowOnsaleBooks() {
    this.setData({ showOnsaleList: true });
  },
  // 关闭在售书籍列表
  onCloseOnsaleList() {
    this.setData({ showOnsaleList: false });
  },
  // 编辑书籍
  onEditBook(e) {
    const id = e.currentTarget.dataset.id;
    const book = this.data.onsaleBooks.find(b => b.id === id);
    this.setData({ showEditBookModal: true, editBook: { ...book } });
  },
  // 编辑弹窗输入
  onEditBookInput(e) {
    const field = e.currentTarget.dataset.field;
    let value = e.detail.value;
    // 价格转数字
    if (field === 'price') value = Number(value);
    this.setData({ [`editBook.${field}`]: value });
  },
  // 保存编辑
  onSaveEditBook() {
    // 修改本地缓存
    let books = this.data.onsaleBooks.map(b => b.id === this.data.editBook.id ? { ...this.data.editBook } : b);
    wx.setStorageSync('publishedBooks', books);
    this.setData({ onsaleBooks: books, showEditBookModal: false });
    wx.showToast({ title: '保存成功', icon: 'success' });
  },
  // 关闭编辑弹窗
  onCloseEditBookModal() {
    this.setData({ showEditBookModal: false });
  },
  // 下架
  onOffBook(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({ showConfirmModal: true, confirmType: 'off', confirmBookId: id });
  },
  // 删除
  onDeleteBook(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({ showConfirmModal: true, confirmType: 'delete', confirmBookId: id });
  },
  // 确认弹窗-确定
  onConfirmModal() {
    const { confirmType, confirmBookId, onsaleBooks } = this.data;
    let books = onsaleBooks;
    if (confirmType === 'off' || confirmType === 'delete') {
      books = onsaleBooks.filter(b => b.id !== confirmBookId);
      wx.setStorageSync('publishedBooks', books);
      this.setData({ onsaleBooks: books, showConfirmModal: false });
      wx.showToast({ title: confirmType === 'off' ? '已下架' : '已删除', icon: 'success' });
    }
  },
  // 确认弹窗-取消
  onCancelModal() {
    this.setData({ showConfirmModal: false });
  },
  // 显示编辑资料弹窗
  showEditModal() {
    const { user, genderOptions } = this.data;
    this.setData({
      showEditModal: true,
      editUser: { ...user },
      editGenderIndex: genderOptions.indexOf(user.gender) >= 0 ? genderOptions.indexOf(user.gender) : 0
    });
  },
  // 关闭弹窗
  onCloseEditModal() {
    this.setData({ showEditModal: false });
  },
  // 输入昵称
  onEditNameInput(e) {
    this.setData({ 'editUser.name': e.detail.value });
  },
  // 输入学校
  onEditSchoolInput(e) {
    this.setData({ 'editUser.school': e.detail.value });
  },
  // 选择性别
  onEditGenderChange(e) {
    const idx = e.detail.value;
    this.setData({ editGenderIndex: idx, 'editUser.gender': this.data.genderOptions[idx] });
  },
  // 选择头像（立即上传到云存储）
  onChooseAvatar() {
    const userInfo = wx.getStorageSync('userInfo');
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        const filePath = res.tempFilePaths[0];
        const ext = filePath.split('.').pop();
        const cloudPath = `avatars/${userInfo._id || Date.now()}.${ext}`;
        wx.showLoading({ title: '上传中...' });
        try {
          // 上传到云存储
          const uploadRes = await wx.cloud.uploadFile({
            cloudPath,
            filePath
          });
          // 上传成功后，保存云文件ID到 editUser.avatar
          this.setData({ 'editUser.avatar': uploadRes.fileID });
          wx.showToast({ title: '上传成功', icon: 'success' });
        } catch (e) {
          wx.showToast({ title: '上传失败', icon: 'none' });
        } finally {
          wx.hideLoading();
        }
      }
    });
  },
  // 保存资料（同步到数据库和本地缓存）
  async onSaveEdit() {
    const userInfo = wx.getStorageSync('userInfo');
    const db = wx.cloud.database();
    const editUser = this.data.editUser;
    if (!userInfo || !userInfo._id) {
      wx.showToast({ title: '用户信息异常', icon: 'none' });
      return;
    }
    wx.showLoading({ title: '保存中...' });
    try {
      // 直接保存editUser.avatar（云文件ID）
      await db.collection('users').doc(userInfo._id).update({
        data: {
          nickname: editUser.name,
          school: editUser.school,
          gender: editUser.gender,
          avatar: editUser.avatar
        }
      });
      // 更新本地缓存
      const newUserInfo = { ...userInfo, nickname: editUser.name, school: editUser.school, gender: editUser.gender, avatar: editUser.avatar };
      wx.setStorageSync('userInfo', newUserInfo);
      this.setData({
        user: { ...this.data.editUser },
        showEditModal: false
      });
      wx.showToast({ title: '保存成功', icon: 'success' });
    } catch (err) {
      wx.showToast({ title: '保存失败', icon: 'none' });
    } finally {
      wx.hideLoading();
    }
  },
  // 显示已售书籍列表
  onShowSoldBooks() {
    this.setData({ showSoldList: true });
  },
  // 关闭已售书籍列表
  onCloseSoldList() {
    this.setData({ showSoldList: false });
  },
  // 显示收藏书籍列表
  onShowFavBooks() {
    this.setData({ showFavList: true });
  },
  // 关闭收藏书籍列表
  onCloseFavList() {
    this.setData({ showFavList: false });
  },
  // 取消收藏
  onUnfavBook(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({ favBooks: this.data.favBooks.filter(b => b.id !== id) });
    wx.showToast({ title: '已取消收藏', icon: 'success' });
  },
  // 显示浏览记录列表
  onShowHistoryBooks() {
    this.setData({ showHistoryList: true });
  },
  // 关闭浏览记录列表
  onCloseHistoryList() {
    this.setData({ showHistoryList: false });
  },
  // 移除浏览记录
  onRemoveHistoryBook(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({ historyBooks: this.data.historyBooks.filter(b => b.id !== id) });
    wx.showToast({ title: '已移除', icon: 'success' });
  },
  onShowCommunityModal() {
    this.setData({ showCommunityModal: true });
  },
  onCloseCommunityModal() {
    this.setData({ showCommunityModal: false });
  },
  onShowWalletModal() {
    this.setData({ showWalletModal: true });
  },
  onCloseWalletModal() {
    this.setData({ showWalletModal: false });
  },
  onShowOrderModal() {
    this.setData({ showOrderModal: true });
  },
  onCloseOrderModal() {
    this.setData({ showOrderModal: false });
  },
  onShowSettingModal() {
    this.setData({ showSettingModal: true });
  },
  onCloseSettingModal() {
    this.setData({ showSettingModal: false });
  },
  goCommunity() {
    wx.navigateTo({ url: '/pages/community/community' });
  },
  goWallet() {
    wx.navigateTo({ url: '/pages/wallet/wallet' });
  },
  goOrders() {
    wx.navigateTo({ url: '/pages/orders/orders' });
  },
  goSettings() {
    wx.navigateTo({ url: '/pages/settings/settings' });
  },
  // 评分实时计算
  updateScore() {
    let score = 100 - this.data.maliciousComments * 5 - this.data.fakeTrades * 20;
    if (score < 0) score = 0;
    let starCount = 5;
    if (score >= 96) starCount = 5;
    else if (score >= 80) starCount = 4;
    else if (score >= 60) starCount = 3;
    else if (score >= 40) starCount = 2;
    else if (score > 0) starCount = 1;
    else starCount = 0;
    this.setData({ score, starCount });
  },
  // 恶意评论检测（模拟）
  detectMaliciousComment() {
    this.setData({ maliciousComments: this.data.maliciousComments + 1 }, () => {
      this.updateScore();
      this.addMessage('检测到恶意评论，已扣5分');
    });
  },
  // 虚假买卖检测（模拟）
  detectFakeTrade() {
    this.setData({ fakeTrades: this.data.fakeTrades + 1 }, () => {
      this.updateScore();
      this.addMessage('检测到虚假买卖，已扣20分');
    });
  },
  // 添加消息
  addMessage(msg) {
    const messages = this.data.messages || [];
    messages.unshift({ content: msg, time: new Date().toLocaleString() });
    this.setData({ messages });
  },
  // 发布书籍前校验信誉分
  canPublish() {
    this.updateScore();
    return this.data.score >= 60;
  },
  // 评论前校验信誉分
  canComment() {
    this.updateScore();
    return this.data.score >= 80;
  },
  onShow() {
    // 页面显示时刷新学号认证状态
    let auth = false;
    try {
      auth = wx.getStorageSync('studentAuth');
    } catch (e) {}
    this.setData({ studentAuth: !!auth });

    // 读取本地缓存的在售书籍
    let publishedBooks = wx.getStorageSync('publishedBooks') || [];
    this.setData({ onsaleBooks: publishedBooks });

    // 读取本地缓存的用户信息
    let userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      // 兼容老数据字段
      this.setData({
        user: {
          avatar: userInfo.avatar || '',
          name: userInfo.nickname || userInfo.name || '',
          id: userInfo._id || userInfo.id || '',
          auth: !!userInfo.auth,
          school: userInfo.school || '',
          gender: userInfo.gender || ''
        }
      });
    }

    this.updateScore();
  },
  onLogout() {
    // 清除本地存储的用户信息
    wx.removeStorageSync('user');
    wx.showToast({ title: '已退出登录', icon: 'success', duration: 1000 });
    setTimeout(() => {
      wx.reLaunch({ url: '/pages/login/login' });
    }, 1000);
  },
}) 