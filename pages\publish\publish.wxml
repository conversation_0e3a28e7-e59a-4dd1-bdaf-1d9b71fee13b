<view class="publish-container">
  <view class="publish-header">
    <text class="publish-title">发布书籍</text>
    <button class="publish-btn" bindtap="onPublish">
      <image src="/static/images/upload-blue.png" class="publish-btn-icon" />
      <text class="publish-btn-text">发布</text>
    </button>
  </view>
  <view class="publish-section">
    <view class="publish-label-row">
      <text class="publish-label">书籍图片</text>
      <text class="publish-tip">最多9张</text>
    </view>
    <view class="publish-img-list">
      <view class="publish-img-upload" bindtap="onChooseImage">
        <text class="publish-img-plus">+</text>
      </view>
      <block wx:for="{{images}}" wx:key="index">
        <image src="{{item}}" class="publish-img-thumb" mode="aspectFill" />
      </block>
    </view>
  </view>
  <view class="publish-section">
    <text class="publish-section-title">基本信息</text>
    <input class="publish-input" placeholder="书名" value="{{bookName}}" bindinput="onBookNameInput" />
    <input class="publish-input" placeholder="作者" value="{{author}}" bindinput="onAuthorInput" />
    <input class="publish-input" placeholder="出版社" value="{{publisher}}" bindinput="onPublisherInput" />
    <input class="publish-input" placeholder="ISBN" value="{{isbn}}" bindinput="onIsbnInput" />
  </view>
  <view class="publish-section">
    <text class="publish-section-title">交易信息</text>
    <input class="publish-input" placeholder="￥ 价格" value="{{price}}" bindinput="onPriceInput" />
    <input class="publish-input" placeholder="￥ 原价" value="{{originPrice}}" bindinput="onOriginPriceInput" />
    <picker class="publish-input" mode="selector" range="{{degreeOptions}}" value="{{degreeIndex}}" bindchange="onDegreeChange">
      <view class="publish-picker-text">{{degreeOptions[degreeIndex] || '请选择新旧程度'}}</view>
    </picker>
    <input class="publish-input" placeholder="库存数量" value="{{stock}}" bindinput="onStockInput" />
    <view class="publish-delivery-list">
      <text class="publish-section-title" style="margin-bottom:12rpx;">送达方式</text>
      <block wx:for="{{deliveryOptions}}" wx:key="index">
        <view class="publish-delivery-item {{deliveryIndex===index?'active':''}}" bindtap="onDeliveryTap" data-index="{{index}}">{{item}}</view>
      </block>
    </view>
  </view>
  <view class="publish-section">
    <text class="publish-section-title">书籍分类</text>
    <view class="publish-category-list">
      <block wx:for="{{categoryOptions}}" wx:key="index">
        <view class="publish-category-item {{categoryIndex===index?'active':''}}" bindtap="onCategoryTap" data-index="{{index}}">{{item}}</view>
      </block>
    </view>
  </view>
  <view class="publish-section">
    <text class="publish-section-title">书籍描述</text>
    <textarea class="publish-textarea" placeholder="请详细描述书籍的品相、使用情况等信息" value="{{desc}}" bindinput="onDescInput" />
  </view>
</view> 