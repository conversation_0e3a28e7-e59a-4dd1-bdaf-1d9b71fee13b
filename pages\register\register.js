const API = require('../../utils/api')

Page({
  data: {
    nickname: '',
    password: '',
    confirmPassword: '',
    phone: '',
    email: '',
    genderOptions: ['男', '女', '保密'],
    genderIndex: 0,
    loading: false
  },
  onNicknameInput(e) {
    this.setData({ nickname: e.detail.value });
  },
  onPasswordInput(e) {
    this.setData({ password: e.detail.value });
  },
  onConfirmPasswordInput(e) {
    this.setData({ confirmPassword: e.detail.value });
  },
  onPhoneInput(e) {
    this.setData({ phone: e.detail.value });
  },
  onEmailInput(e) {
    this.setData({ email: e.detail.value });
  },
  onGenderChange(e) {
    this.setData({ genderIndex: e.detail.value });
  },
  async onRegister() {
    // 表单验证
    if (!this.data.phone || !this.data.password || !this.data.confirmPassword || !this.data.nickname) {
      API.showError('请填写完整信息')
      return
    }

    // 校验邮箱格式（如果填写了邮箱）
    if (this.data.email) {
      const emailReg = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
      if (!emailReg.test(this.data.email)) {
        API.showError('邮箱格式不正确')
        return
      }
    }

    // 校验手机号格式
    const phoneReg = /^1[3-9]\d{9}$/;
    if (!phoneReg.test(this.data.phone)) {
      API.showError('手机号格式不正确')
      return
    }

    // 校验密码强度（至少6位）
    if (this.data.password.length < 6) {
      API.showError('密码至少需要6位')
      return
    }

    if (this.data.password !== this.data.confirmPassword) {
      API.showError('两次密码输入不一致')
      return
    }

    this.setData({ loading: true })

    try {
      const registerData = {
        phone: this.data.phone,
        password: this.data.password,
        nickname: this.data.nickname,
        email: this.data.email || '',
        gender: this.data.genderOptions[this.data.genderIndex]
      }

      const result = await API.register(registerData)

      if (result.success) {
        API.showSuccess('注册成功')

        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/login/login'
          })
        }, 1500)
      } else {
        API.showError(result.message)
      }
    } catch (err) {
      console.error('注册失败：', err)
      API.showError('注册失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },
  goLogin() {
    wx.navigateTo({ url: '/pages/login/login' });
  }
}); 