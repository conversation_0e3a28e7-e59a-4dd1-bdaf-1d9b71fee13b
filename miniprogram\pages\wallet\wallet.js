Page({
  data: {
    showRechargeModal: false,
    showWithdrawModal: false,
    rechargeAmount: '',
    withdrawAmount: ''
  },
  onRecharge() { this.setData({ showRechargeModal: true }); },
  onCloseRechargeModal() { this.setData({ showRechargeModal: false, rechargeAmount: '' }); },
  onRechargeInput(e) { this.setData({ rechargeAmount: e.detail.value }); },
  onConfirmRecharge() {
    if (!this.data.rechargeAmount) return wx.showToast({ title: '请输入金额', icon: 'none' });
    this.setData({ showRechargeModal: false, rechargeAmount: '' });
    wx.showToast({ title: '充值成功', icon: 'success' });
  },
  onWithdraw() { this.setData({ showWithdrawModal: true }); },
  onCloseWithdrawModal() { this.setData({ showWithdrawModal: false, withdrawAmount: '' }); },
  onWithdrawInput(e) { this.setData({ withdrawAmount: e.detail.value }); },
  onConfirmWithdraw() {
    if (!this.data.withdrawAmount) return wx.showToast({ title: '请输入金额', icon: 'none' });
    this.setData({ showWithdrawModal: false, withdrawAmount: '' });
    wx.showToast({ title: '提现成功', icon: 'success' });
  }
}) 