const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})
const db = cloud.database()
const _ = db.command

// 钱包管理云函数
exports.main = async (event, context) => {
  const { type, data } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  switch (type) {
    // 获取钱包信息
    case 'getWalletInfo':
      try {
        // 获取钱包基本信息
        let wallet = await db.collection('wallets').doc(openid).get()
        
        if (!wallet.data) {
          // 创建新钱包
          const newWallet = {
            _id: openid,
            balance: 0,
            totalIncome: 0,
            totalExpense: 0,
            createTime: db.serverDate(),
            updateTime: db.serverDate()
          }
          await db.collection('wallets').add({ data: newWallet })
          wallet = { data: newWallet }
        }

        // 获取最近的交易记录
        const transactions = await db.collection('transactions')
          .where({ userId: openid })
          .orderBy('createTime', 'desc')
          .limit(20)
          .get()

        return {
          code: 0,
          data: {
            ...wallet.data,
            transactions: transactions.data
          },
          message: '获取成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '获取钱包信息失败',
          error: error.message
        }
      }

    // 充值
    case 'recharge':
      try {
        const { amount, paymentMethod = 'wechat' } = data
        
        if (!amount || amount <= 0) {
          return {
            code: 400,
            message: '充值金额必须大于0'
          }
        }

        // 更新钱包余额
        await db.collection('wallets').doc(openid).update({
          data: {
            balance: _.inc(amount),
            totalIncome: _.inc(amount),
            updateTime: db.serverDate()
          }
        })

        // 添加交易记录
        await db.collection('transactions').add({
          data: {
            userId: openid,
            type: 'income',
            amount: amount,
            description: `${paymentMethod}充值`,
            relatedId: '',
            createTime: db.serverDate()
          }
        })

        return {
          code: 0,
          message: '充值成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '充值失败',
          error: error.message
        }
      }

    // 提现
    case 'withdraw':
      try {
        const { amount, accountType, accountInfo } = data
        
        if (!amount || amount <= 0) {
          return {
            code: 400,
            message: '提现金额必须大于0'
          }
        }

        // 检查余额是否足够
        const wallet = await db.collection('wallets').doc(openid).get()
        if (!wallet.data || wallet.data.balance < amount) {
          return {
            code: 400,
            message: '余额不足'
          }
        }

        // 更新钱包余额
        await db.collection('wallets').doc(openid).update({
          data: {
            balance: _.inc(-amount),
            totalExpense: _.inc(amount),
            updateTime: db.serverDate()
          }
        })

        // 添加交易记录
        await db.collection('transactions').add({
          data: {
            userId: openid,
            type: 'expense',
            amount: amount,
            description: `提现到${accountType}`,
            relatedId: '',
            createTime: db.serverDate()
          }
        })

        return {
          code: 0,
          message: '提现申请成功，预计1-3个工作日到账'
        }
      } catch (error) {
        return {
          code: -1,
          message: '提现失败',
          error: error.message
        }
      }

    // 转账（用于交易结算）
    case 'transfer':
      try {
        const { toUserId, amount, description, relatedId } = data
        
        if (!amount || amount <= 0) {
          return {
            code: 400,
            message: '转账金额必须大于0'
          }
        }

        // 检查发送方余额
        const fromWallet = await db.collection('wallets').doc(openid).get()
        if (!fromWallet.data || fromWallet.data.balance < amount) {
          return {
            code: 400,
            message: '余额不足'
          }
        }

        // 检查接收方钱包是否存在
        let toWallet = await db.collection('wallets').doc(toUserId).get()
        if (!toWallet.data) {
          // 创建接收方钱包
          const newWallet = {
            _id: toUserId,
            balance: 0,
            totalIncome: 0,
            totalExpense: 0,
            createTime: db.serverDate(),
            updateTime: db.serverDate()
          }
          await db.collection('wallets').add({ data: newWallet })
        }

        // 扣除发送方余额
        await db.collection('wallets').doc(openid).update({
          data: {
            balance: _.inc(-amount),
            totalExpense: _.inc(amount),
            updateTime: db.serverDate()
          }
        })

        // 增加接收方余额
        await db.collection('wallets').doc(toUserId).update({
          data: {
            balance: _.inc(amount),
            totalIncome: _.inc(amount),
            updateTime: db.serverDate()
          }
        })

        // 添加发送方交易记录
        await db.collection('transactions').add({
          data: {
            userId: openid,
            type: 'expense',
            amount: amount,
            description: description || '转账支出',
            relatedId: relatedId || '',
            createTime: db.serverDate()
          }
        })

        // 添加接收方交易记录
        await db.collection('transactions').add({
          data: {
            userId: toUserId,
            type: 'income',
            amount: amount,
            description: description || '转账收入',
            relatedId: relatedId || '',
            createTime: db.serverDate()
          }
        })

        return {
          code: 0,
          message: '转账成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '转账失败',
          error: error.message
        }
      }

    // 获取交易记录
    case 'getTransactions':
      try {
        const { page = 1, pageSize = 20, type: transType } = data
        let query = { userId: openid }
        
        if (transType && ['income', 'expense'].includes(transType)) {
          query.type = transType
        }

        const result = await db.collection('transactions')
          .where(query)
          .orderBy('createTime', 'desc')
          .skip((page - 1) * pageSize)
          .limit(pageSize)
          .get()

        return {
          code: 0,
          data: {
            list: result.data,
            total: result.data.length,
            page,
            pageSize
          },
          message: '获取成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '获取交易记录失败',
          error: error.message
        }
      }

    default:
      return {
        code: 404,
        message: '不支持的操作类型'
      }
  }
}
