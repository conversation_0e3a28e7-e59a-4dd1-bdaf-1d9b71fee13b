快速开始
API 使用前提：已获取API Key并完成配置API Key到环境变量。如果通过SDK调用，需要安装 OpenAI 或 DashScope SDK（DashScope Java SDK 版本需要不低于2.18.2）。

如果您首次使用阿里云百炼，请参考首次调用通义千问API文档进行百炼服务的开通与计算环境的配置，并将步骤 2：调用大模型API代码中的model参数修改为上表中您需要调用的模型名称。
由于 DeepSeek-R1 类模型的思考过程可能较长，可能导致响应慢或超时，建议您优先使用流式输出方式调用。
OpenAI兼容DashScope
您可以通过 OpenAI SDK 或 OpenAI 兼容的HTTP方式快速体验DeepSeek模型。

PythonNode.jsHTTP
示例代码
 
import OpenAI from "openai";

const openai = new OpenAI(
    {
        // 若没有配置环境变量，请用阿里云百炼API Key将下行替换为：apiKey: "sk-xxx",
        apiKey: process.env.DASHSCOPE_API_KEY,
        baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1"
    }
);
const completion = await openai.chat.completions.create({
    model: "deepseek-r1",  // 此处以 deepseek-r1 为例，可按需更换模型名称。
    messages: [
        { role: "user", content: "9.9和9.11谁大" }
    ],
});
console.log("思考过程：")
console.log(completion.choices[0].message.reasoning_content)
console.log("最终答案：")
console.log(completion.choices[0].message.content)
返回结果
 
思考过程：

嗯，我现在要比较9.9和9.11这两个数，看看哪个更大。刚开始的时候，可能会有点混淆，特别是因为小数点后的位数不一样，所以需要仔细分析一下。让我一步一步来思考这个问题。

首先，我知道比较小数的时候，应该先比较整数部分，如果整数部分大的那个数就更大。如果整数部分相同，再依次比较小数部分的每一位数字，直到找到差异为止。这里两个数的整数部分都是9，所以整数部分相等，接下来就需要比较小数部分了。

接下来，我需要看看小数部分。9.9的小数部分是0.9，也就是十分位上的数字是9，而百分位上的数字是0，因为9.9其实可以写成9.90。而9.11的小数部分是0.11，十分位是1，百分位是1。现在的问题是，如何比较这两个小数部分的大小。

...

或者，用时间来做例子，假设是9.9小时和9.11小时，9.9小时等于9小时54分钟（因为0.9小时×60分钟=54分钟），而9.11小时是9小时6.6分钟（0.11×60≈6.6分钟），显然，9小时54分钟比9小时6分钟多得多，所以9.9小时更大。

通过这些不同的例子，都验证了9.9确实比9.11大。因此，可以确定答案是正确的。

最终答案：
9.9比9.11大。

**步骤解析：**

1. **整数部分比较**：两个数的整数部分均为9，因此需进一步比较小数部分。
2. **小数部分对齐位数**：将9.9补全为9.90，与9.11的小数部分对齐（0.90 vs 0.11）。
3. **逐位比较小数部分**：
   - **十分位**：9（来自9.90） > 1（来自9.11），无需继续比较后续位数。
4. **结论**：由于十分位上9 > 1，故9.90（即9.9）大于9.11。

**验证方法：**
- **减法验证**：9.90 - 9.11 = 0.79（正数，说明9.9更大）。
- **分数转换**：9.9 = 990/100，9.11 = 911/100，990 > 911。
- **实际意义**：如货币中9.9元（9元9角）多于9.11元（9元1角1分）。

综上，**9.9 > 9.11**。
多轮对话
阿里云百炼提供的 DeepSeek API 默认不会记录您的历史对话信息。多轮对话功能可以让大模型“拥有记忆”，满足如追问、信息采集等需要连续交流的场景。如果您使用 DeepSeek-R1 类模型，会收到reasoning_content字段（思考过程）与content（回复内容），您可以将content字段通过{'role': 'assistant', 'content':API 返回的content}添加到上下文，无需添加reasoning_content字段。

OpenAI兼容DashScope
您可以通过 OpenAI SDK 或 OpenAI 兼容的 HTTP 方式使用多轮对话功能。

PythonNode.jsHTTP
示例代码
 
import OpenAI from "openai";
import * as readline from 'readline';

const openai = new OpenAI({
    apiKey: process.env.DASHSCOPE_API_KEY,
    baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1"
});

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

let messages = [];

async function chat() {
    while (true) {
        const userInput = await new Promise(resolve => {
            rl.question('用户：', resolve);
        });

        if (userInput.toLowerCase() === '退出') {
            break;
        }

        messages.push({ role: "user", content: userInput });

        try {
            const completion = await openai.chat.completions.create({
                model: "deepseek-r1",
                messages: messages
            });

            // 使用纯文本符号分隔不同区域
            console.log('\n===== 用户输入 =====');
            console.log(userInput);
            
            console.log('\n----- 思考过程 -----');
            console.log(completion.choices[0].message.reasoning_content);
            
            console.log('\n===== 助手回复 =====');
            const aiResponse = completion.choices[0].message.content;
            console.log(aiResponse);
            
            console.log('\n====================\n');
            
            messages.push({ role: "assistant", content: aiResponse });
        } catch (error) {
            console.error('发生错误：', error);
            break;
        }
    }
    rl.close();
}

console.log('请输入内容（输入"退出"结束对话）');
chat();
流式输出
DeepSeek-R1 类模型可能会输出较长的思考过程，为了降低超时风险，建议您使用流式输出方式调用 DeepSeek-R1 类模型。

OpenAI兼容DashScope
PythonNode.jsHTTP
示例代码
 
import OpenAI from "openai";
import process from 'process';

// 初始化 openai 客户端
const openai = new OpenAI({
    apiKey: process.env.DASHSCOPE_API_KEY, // 从环境变量读取
    baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
});

let reasoningContent = '';
let answerContent = '';
let isAnswering = false;

async function main() {
    try {
        const stream = await openai.chat.completions.create({
            model: 'deepseek-r1',
            messages: [{ role: 'user', content: '9.9和9.11谁大' }],
            stream: true
        });

        console.log('\n' + '='.repeat(20) + '思考过程' + '='.repeat(20) + '\n');

        for await (const chunk of stream) {
            if (!chunk.choices?.length) {
                console.log('\nUsage:');
                console.log(chunk.usage);
                continue;
            }

            const delta = chunk.choices[0].delta;
            
            // 处理思考过程
            if (delta.reasoning_content) {
                process.stdout.write(delta.reasoning_content);
                reasoningContent += delta.reasoning_content;
            } 
            // 处理正式回复
            else if (delta.content) {
                if (!isAnswering) {
                    console.log('\n' + '='.repeat(20) + '完整回复' + '='.repeat(20) + '\n');
                    isAnswering = true;
                }
                process.stdout.write(delta.content);
                answerContent += delta.content;
            }
        }
    } catch (error) {
        console.error('Error:', error);
    }
}

main();
返回结果
 
====================思考过程====================

嗯，今天老师布置了一个问题，要比较9.9和9.11哪个大。一开始看起来好像挺简单的，但仔细想想可能有哪里需要注意的地方吧。让我仔细想想看。

首先，我需要明确这两个数的形式。9.9应该是一个小数，也就是9又十分之九，对吧？而9.11可能是9又百分之十一，也就是9.11。不过有时候小数点后面可能有不同的位数，比如有时候会写成9.1和9.10，这时候位数不同但数值其实是一样的，比如9.1等于9.10，因为后面的0不影响大小。不过这里的情况是9.9和9.11，它们的位数不同，一个是十分位，一个是百分位，所以可能需要转换一下单位来比较。

...

911/100 保持不变

所以比较990/100和911/100，显然990比911大，所以990/100也就是99/10即9.9更大。

所以结论是9.9比9.11大。

不过可能还是有人会混淆，因为看到9.11的小数点后有两位，可能会觉得比一位的大，但实际上是小数点后的每一位代表的是更小的单位。比如十分位是0.1，百分位是0.01，所以第一位小数是十分位，第二位是百分位，所以9.9的十分位是9，也就是0.9，而9.11的十分位是1，百分位是1，所以总共是0.11，所以显然0.9比0.11大很多。

因此，最终的结论应该是9.9大于9.11。
====================完整回复====================

要比较9.9和9.11的大小，可以按照以下步骤进行：

1. **统一小数位数**：将9.9转换为9.90（保持两位小数），以便与9.11直接比较。
2. **逐位比较**：
   - **整数部分**：两者整数部分均为9，相等。
   - **小数部分**：比较0.90（9.90的小数部分）与0.11（9.11的小数部分）。显然，0.90 > 0.11。
3. **结论**：由于小数部分0.90 > 0.11，因此**9.9 > 9.11**。

**答案：9.9比9.11大。**
注意事项
稳定性：如果执行后没有响应、响应超时或者报错An internal error has occured, please try again later or contact service support，请尝试重试或者更换其他 DeepSeek 模型，也可以尝试使用 Qwen 最新的 QwQ 模型（可替代 deepseek-r1 模型）或qwen-max-2025-01-25（可替代 deepseek-v3 模型）。

高峰期任务可能排队或失败，阿里云百炼持续扩容中，调用失败请稍后重试。
DeepSeek-R1 类模型

不支持的功能

Function Calling、JSON Output、对话前缀续写、上下文硬盘缓存

不支持的参数

temperature、top_p、presence_penalty、frequency_penalty、logprobs、top_logprobs

设置这些参数都不会生效，即使没有输出错误提示。
DeepSeek官方不建议设置 System Message，原文："Avoid adding a system prompt; all instructions should be contained within the user prompt."

DeepSeek-V3：

参数默认值：

temperature：0.7（取值范围是[0:2)）；

top_p：0.6；

presence_penalty：0.95。

不支持设置的参数和功能：frequency_penalty、logprobs、top_logprobs参数；不支持 Function Call、JSON Output 等功能，敬请关注后续动态。

阿里云百炼应用：已支持接入 DeepSeek 模型。

模型在线体验：已支持体验 DeepSeek 模型。

联网搜索

模型调用当前暂不支持联网搜索。

深度思考：只要调用 DeepSeek-R1 类模型即代表开启深度思考（深度思考过程通过reasoning_content返回）。