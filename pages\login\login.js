const API = require('../../utils/api')

Page({
  data: {
    loginType: 'wechat', // 默认微信登录
    phone: '',
    password: '',
    nickname: '',
    loading: false,
    canIUseGetUserProfile: false
  },

  onLoad() {
    // 检查是否已经登录
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      wx.redirectTo({
        url: '/pages/user-center/user-center'
      })
    }

    // 检查是否支持getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
  },

  onTabChange(e) {
    this.setData({ loginType: e.currentTarget.dataset.type });
  },

  onPhoneInput(e) {
    this.setData({ phone: e.detail.value });
  },

  onPasswordInput(e) {
    this.setData({ password: e.detail.value });
  },

  onNicknameInput(e) {
    this.setData({ nickname: e.detail.value });
  },

  async onPhoneLogin() {
    if (!this.data.phone || !this.data.password) {
      API.showError('请填写完整信息')
      return
    }

    // 简单的手机号格式验证
    const phoneReg = /^1[3-9]\d{9}$/
    if (!phoneReg.test(this.data.phone)) {
      API.showError('请输入正确的手机号')
      return
    }

    this.setData({ loading: true })

    try {
      const result = await API.call('login', 'phoneLogin', {
        phone: this.data.phone,
        password: this.data.password
      })

      if (result.success) {
        // 保存用户信息到本地
        wx.setStorageSync('userInfo', result.data.userInfo)
        wx.setStorageSync('openid', result.data.openid)

        API.showSuccess('登录成功')

        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/index/index'
          })
        }, 1500)
      } else {
        API.showError(result.message)
      }
    } catch (err) {
      console.error('登录失败：', err)
      API.showError('登录失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  async onWechatLogin() {
    this.setData({ loading: true })

    try {
      let userInfo = null

      // 获取用户信息
      if (this.data.canIUseGetUserProfile) {
        const res = await wx.getUserProfile({
          desc: '用于完善用户资料'
        })
        userInfo = res.userInfo
      } else {
        // 兼容旧版本
        const res = await wx.getUserInfo()
        userInfo = res.userInfo
      }

      // 调用登录云函数
      const result = await API.call('login', 'wechatLogin', { userInfo })

      if (result.success) {
        // 保存用户信息到本地
        wx.setStorageSync('userInfo', result.data.userInfo)
        wx.setStorageSync('openid', result.data.openid)

        API.showSuccess('登录成功')

        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/index/index'
          })
        }, 1500)
      } else {
        API.showError(result.message)
      }
    } catch (err) {
      console.error('微信登录失败：', err)
      if (err.errMsg && err.errMsg.includes('cancel')) {
        API.showError('用户取消授权')
      } else {
        API.showError('登录失败，请重试')
      }
    } finally {
      this.setData({ loading: false })
    }
  },

  async onNicknameLogin() {
    if (!this.data.nickname || !this.data.password) {
      API.showError('请填写完整信息')
      return
    }

    this.setData({ loading: true })

    try {
      const result = await API.call('login', 'nicknameLogin', {
        nickname: this.data.nickname,
        password: this.data.password
      })

      if (result.success) {
        // 保存用户信息到本地
        wx.setStorageSync('userInfo', result.data.userInfo)
        wx.setStorageSync('openid', result.data.openid)

        API.showSuccess('登录成功')

        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/index/index'
          })
        }, 1500)
      } else {
        API.showError(result.message)
      }
    } catch (err) {
      console.error('登录失败：', err)
      API.showError('登录失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  goRegister() {
    wx.navigateTo({ url: '/pages/register/register' });
  }
}); 