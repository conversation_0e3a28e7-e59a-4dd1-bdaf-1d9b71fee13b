Component({
  properties: {
    active: {
      type: Number,
      value: 0
    }
  },
  methods: {
    onTabbarTap(e) {
      const index = Number(e.currentTarget.dataset.index);
      if (index === this.data.active) return;
      this.triggerEvent('change', { index });
      if (index === 0) {
        wx.redirectTo({ url: '/pages/index/index' });
      } else if (index === 4) {
        wx.redirectTo({ url: '/pages/user-center/user-center' });
      } else {
        wx.showToast({ title: '功能开发中', icon: 'none' });
      }
    },
    onTabbarPublish() {
      wx.navigateTo({ url: '/pages/publish/publish' });
    },
    onTabbarMessage() {
      if (this.data.active === 3) return;
      wx.redirectTo({ url: '/pages/message/message' });
    },
    onTabbarDiscover() {
      if (this.data.active === 1) return;
      wx.redirectTo({ url: '/pages/discover/discover' });
    }
  }
}); 