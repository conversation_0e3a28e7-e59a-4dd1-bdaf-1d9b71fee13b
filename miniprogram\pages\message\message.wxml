<view class="msg-container">
  <view class="msg-header">
    <text class="msg-title">消息</text>
    <image src="/static/images/more.png" class="msg-more-icon" />
  </view>
  <view class="msg-tabbar">
    <view class="msg-tab {{activeTab===0?'active':''}}" bindtap="onTabTap" data-index="0">全部</view>
    <view class="msg-tab {{activeTab===1?'active':''}}" bindtap="onTabTap" data-index="1">交易</view>
    <view class="msg-tab {{activeTab===2?'active':''}}" bindtap="onTabTap" data-index="2">系统</view>
    <view class="msg-tab {{activeTab===3?'active':''}}" bindtap="onTabTap" data-index="3">通知</view>
    <view class="msg-tab-underline" style="left:{{activeTab*25}}%;"></view>
  </view>
  <view class="msg-list">
    <block wx:for="{{msgList}}" wx:key="id">
      <view class="msg-item {{item.type}}" bindtap="onMsgTap" data-index="{{index}}">
        <view class="msg-avatar-box">
          <image src="{{item.avatar}}" class="msg-avatar" />
          <view wx:if="item.unread" class="msg-unread-dot">{{item.unread}}</view>
        </view>
        <view class="msg-content-box">
          <view class="msg-row">
            <text class="msg-nick">{{item.nick}}</text>
            <text class="msg-time">{{item.time}}</text>
          </view>
          <text class="msg-title2">{{item.title}}</text>
          <text class="msg-desc">{{item.desc}}</text>
        </view>
      </view>
    </block>
  </view>
  <!-- 对话框弹窗 -->
  <view wx:if="{{showDialog}}" class="msg-dialog-mask">
    <view class="msg-dialog">
      <view class="msg-dialog-header">
        <image src="{{dialogMsg.avatar}}" class="msg-dialog-avatar" />
        <text class="msg-dialog-nick">{{dialogMsg.nick}}</text>
        <text class="msg-dialog-close" bindtap="onCloseDialog">×</text>
      </view>
      <view class="msg-dialog-body">
        <block wx:for="{{dialogMsgList}}" wx:key="idx">
          <view class="msg-dialog-row {{item.from==='me'?'me':'other'}}">
            <text class="msg-dialog-bubble">{{item.text}}</text>
          </view>
        </block>
      </view>
      <view class="msg-dialog-inputbar">
        <input class="msg-dialog-input" placeholder="请输入消息..." value="{{dialogInput}}" bindinput="onDialogInput" />
        <button class="msg-dialog-sendbtn" bindtap="onSendDialog">发送</button>
      </view>
    </view>
  </view>
  <view class="system-message-list" wx:if="{{systemMessages.length}}">
    <view class="system-message-title">系统通知</view>
    <block wx:for="{{systemMessages}}" wx:key="time">
      <view class="system-message-item">
        <text class="system-message-content">{{item.content}}</text>
        <text class="system-message-time">{{item.time}}</text>
      </view>
    </block>
  </view>
  <tabbar active="3" />
</view> 