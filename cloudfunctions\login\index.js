// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { type, data } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  switch (type) {
    // 微信登录
    case 'wechatLogin':
      try {
        // 检查用户是否已存在
        let user = await db.collection('users').doc(openid).get()

        if (!user.data) {
          // 创建新用户
          const newUser = {
            _id: openid,
            nickname: data.userInfo?.nickName || '新用户',
            avatar: data.userInfo?.avatarUrl || '',
            phone: '',
            email: '',
            gender: data.userInfo?.gender === 1 ? '男' : data.userInfo?.gender === 2 ? '女' : '保密',
            school: '',
            studentId: '',
            isVerified: false,
            creditScore: 100,
            maliciousComments: 0,
            fakeTrades: 0,
            createTime: db.serverDate(),
            updateTime: db.serverDate(),
            lastLoginTime: db.serverDate()
          }

          await db.collection('users').add({ data: newUser })
          user = { data: newUser }
        } else {
          // 更新最后登录时间
          await db.collection('users').doc(openid).update({
            data: {
              lastLoginTime: db.serverDate()
            }
          })
        }

        return {
          code: 0,
          data: {
            openid,
            userInfo: user.data
          },
          message: '登录成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '登录失败',
          error: error.message
        }
      }

    // 手机号登录
    case 'phoneLogin':
      try {
        const { phone, code } = data

        // 这里应该验证短信验证码，暂时跳过
        // TODO: 集成短信验证服务

        // 查找用户
        const result = await db.collection('users')
          .where({ phone })
          .get()

        if (result.data.length === 0) {
          return {
            code: 404,
            message: '手机号未注册'
          }
        }

        const user = result.data[0]

        // 更新最后登录时间
        await db.collection('users').doc(user._id).update({
          data: {
            lastLoginTime: db.serverDate()
          }
        })

        return {
          code: 0,
          data: {
            openid: user._id,
            userInfo: user
          },
          message: '登录成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '登录失败',
          error: error.message
        }
      }

    // 昵称登录
    case 'nicknameLogin':
      try {
        const { nickname, password } = data

        if (!nickname || !password) {
          return {
            code: 400,
            message: '昵称和密码不能为空'
          }
        }

        // 查找用户
        const result = await db.collection('users')
          .where({
            nickname,
            password // 注意：实际项目中应该使用加密密码比较
          })
          .get()

        if (result.data.length === 0) {
          return {
            code: 401,
            message: '昵称或密码错误'
          }
        }

        const user = result.data[0]

        // 更新最后登录时间
        await db.collection('users').doc(user._id).update({
          data: {
            lastLoginTime: db.serverDate()
          }
        })

        return {
          code: 0,
          data: {
            openid: user._id,
            userInfo: user
          },
          message: '登录成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '登录失败',
          error: error.message
        }
      }

    // 获取基本信息（兼容旧版本）
    case 'getContext':
    default:
      return {
        code: 0,
        data: {
          openid: wxContext.OPENID,
          appid: wxContext.APPID,
          unionid: wxContext.UNIONID
        },
        message: '获取成功'
      }
  }
}