.container {
  background: #f5ede6;
  min-height: 100vh;
  padding-bottom: 80rpx;
}
/* 顶部导航栏样式 */
.navigation-bar-title, .header-title {
  color: #fff !important;
}
.header-title { display: none; }
.search-title {
  color: #2176ff;
  font-size: 30rpx;
  font-weight: bold;
  margin-right: 18rpx;
}
.search-bar {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 32rpx;
  padding: 0 24rpx;
  margin: 0 24rpx 24rpx 24rpx;
  height: 64rpx;
  box-shadow: 0 2rpx 8rpx #e5e7eb;
}
.search-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  margin: 0 12rpx;
  font-size: 28rpx;
}
.banner-swiper {
  width: 100%;
  height: 200rpx;
  margin-bottom: 24rpx;
  position: relative;
}
.banner-img {
  width: 100%;
  height: 200rpx;
  border-radius: 16rpx;
}
.banner-text-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0,0,0,0.28);
  border-radius: 16rpx;
}
.banner-title {
  color: #fff;
  font-size: 38rpx;
  font-weight: bold;
  text-align: center;
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.18);
  margin-bottom: 12rpx;
}
.banner-desc {
  color: #fff;
  font-size: 24rpx;
  text-align: center;
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.18);
}
.category-list {
  display: flex;
  justify-content: space-around;
  margin: 0 0 32rpx 0;
}
.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.category-icon-bg {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}
.category-bg-blue { background: #e6f0ff; }
.category-bg-green { background: #e6fae6; }
.category-bg-yellow { background: #fffbe6; }
.category-bg-red { background: #ffeaea; }
.category-bg-purple { background: #f5eaff; }
.category-icon {
  width: 48rpx;
  height: 48rpx;
}
.category-text {
  font-size: 24rpx;
  color: #333;
}
.section {
  margin: 0 24rpx 32rpx 24rpx;
}
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.more-link {
  font-size: 24rpx;
  color: #2257e7;
}
.book-list {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx 4%;
  justify-content: flex-start;
}
.book-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx #e5e7eb;
  width: 48%;
  margin-bottom: 24rpx;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-sizing: border-box;
  position: relative;
}
.book-cover {
  width: 100%;
  height: 160rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
  background: #f3f4f6;
  object-fit: contain;
}
.book-title {
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
  color: #222;
}
.book-price {
  color: #e53935;
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}
.book-desc {
  font-size: 22rpx;
  color: #888;
}
.fab { display: none !important; }
.tabbar {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100rpx;
  background: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -2rpx 8rpx #e5e7eb;
  z-index: 9;
}
.tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 22rpx;
  color: #888;
}
.tabbar-text.active {
  color: #2176ff !important;
  font-weight: bold;
}
.tabbar-item .tabbar-icon {
  filter: grayscale(1) brightness(1.2);
}
.tabbar-item .tabbar-text {
  color: #888;
}
.tabbar-item .tabbar-icon,
.tabbar-item .tabbar-text {
  transition: color 0.2s, filter 0.2s;
}
.tabbar-item .tabbar-text.active,
.tabbar-item.active .tabbar-icon {
  color: #2176ff !important;
  filter: none;
}
.tabbar-item-add {
  color: #2176ff !important;
}
.tabbar-item-add .tabbar-icon-add {
  filter: none !important;
}
.search-filter-icon {
  width: 36rpx;
  height: 36rpx;
  margin-left: 8rpx;
}
.book-degree-tag {
  position: absolute;
  right: 16rpx;
  top: 16rpx;
  padding: 0 18rpx;
  height: 40rpx;
  line-height: 40rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
  z-index: 2;
}
.degree-blue { background: #3b82f6; }
.degree-purple { background: #a78bfa; }
.degree-red { background: #f87171; }
.degree-green { background: #34d399; }
.book-author {
  font-size: 22rpx;
  color: #888;
  margin-bottom: 4rpx;
}
.book-info-row {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}
.book-time {
  font-size: 20rpx;
  color: #bdbdbd;
}
.search-bar-black { background: none; }
.search-logo {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}
.tabbar-black { background: none; }
.tabbar-black .tabbar-item { color: inherit; }
.tabbar-black .tabbar-item.active { color: inherit; }
.tabbar-icon {
  width: 40rpx;
  height: 40rpx;
  display: block;
  margin-bottom: 4rpx;
  filter: grayscale(1) brightness(1.2);
  transition: filter 0.2s;
}
.tabbar-icon.active {
  filter: none;
}
.tabbar-text {
  color: #888;
  transition: color 0.2s;
  font-size: 22rpx;
}
.tabbar-text.active {
  color: #2176ff !important;
  font-weight: bold;
}
.tabbar-icon-publish {
  filter: none !important;
}
.tabbar-text-publish {
  color: #2176ff !important;
  font-weight: bold;
}
.tabbar-item-add {
  color: #2176ff !important;
}
.tabbar-item-add .tabbar-icon-add {
  filter: none !important;
}
.search-filter-icon {
  width: 36rpx;
  height: 36rpx;
  margin-left: 8rpx;
}
.book-degree-tag {
  position: absolute;
  right: 16rpx;
  top: 16rpx;
  padding: 0 18rpx;
  height: 40rpx;
  line-height: 40rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
  z-index: 2;
}
.degree-blue { background: #3b82f6; }
.degree-purple { background: #a78bfa; }
.degree-red { background: #f87171; }
.degree-green { background: #34d399; }
.book-author {
  font-size: 22rpx;
  color: #888;
  margin-bottom: 4rpx;
}
.book-info-row {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}
.book-time {
  font-size: 20rpx;
  color: #bdbdbd;
}
.search-bar-black { background: none; }
.search-logo {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}
.tabbar-black { background: none; }
.tabbar-black .tabbar-item { color: inherit; }
.tabbar-black .tabbar-item.active { color: inherit; }
.tabbar-icon {
  width: 40rpx;
  height: 40rpx;
  display: block;
  margin-bottom: 4rpx;
}
.tabbar-icon-add {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 0;
  margin-top: -12rpx;
}
.ai-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.25);
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ai-dialog {
  background: #fff;
  border-radius: 20rpx;
  padding: 48rpx 36rpx 32rpx 36rpx;
  min-width: 400rpx;
  box-shadow: 0 8rpx 32rpx #b3c6ff44;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.ai-dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2176ff;
  margin-bottom: 18rpx;
}
.ai-dialog-content {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 32rpx;
  text-align: center;
}
.ai-dialog-btn {
  background: #2176ff;
  color: #fff;
  border-radius: 24rpx;
  font-size: 26rpx;
  padding: 12rpx 48rpx;
}
.ai-fab {
  position: fixed;
  right: 40rpx;
  bottom: 160rpx;
  width: 100rpx;
  height: 100rpx;
  background: #2176ff;
  border-radius: 50%;
  box-shadow: 0 4rpx 16rpx #b3c6ff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}
.ai-fab-icon {
  width: 60rpx;
  height: 60rpx;
}
.ai-fab-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 101;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
.ai-fab-dialog {
  width: 90vw;
  max-width: 600rpx;
  height: 70vh;
  min-height: 500rpx;
  background: #f8fafc;
  border-radius: 16rpx 16rpx 0 0;
  box-shadow: 0 -2rpx 16rpx #b3c6ff44;
  margin: 0 24rpx 24rpx 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.ai-fab-dialog-header {
  background: #2176ff;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 18rpx 24rpx;
  font-size: 28rpx;
  font-weight: bold;
  position: relative;
}
.ai-fab-dialog-header-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}
.ai-fab-dialog-header-title {
  flex: 1;
}
.ai-fab-dialog-header-close {
  font-size: 40rpx;
  font-weight: normal;
  position: absolute;
  right: 24rpx;
  top: 10rpx;
  color: #fff;
  z-index: 2;
}
.ai-fab-dialog-msg-list {
  flex: 1;
  overflow-y: auto;
  padding: 32rpx 24rpx 0 24rpx;
}
.ai-fab-dialog-msg-ai {
  background: #dbeafe;
  color: #222;
  border-radius: 20rpx;
  padding: 24rpx;
  font-size: 28rpx;
  margin-bottom: 18rpx;
}
.ai-fab-dialog-inputbar {
  display: flex;
  align-items: center;
  padding: 18rpx 24rpx;
  background: #fff;
  border-top: 1rpx solid #e5e7eb;
}
.ai-fab-dialog-input {
  flex: 4;
  height: 60rpx;
  border-radius: 16rpx;
  border: 2rpx solid #2176ff;
  padding: 0 24rpx;
  font-size: 26rpx;
  background: #f8fafc;
  margin-right: 18rpx;
  box-sizing: border-box;
}
.ai-fab-dialog-sendbtn {
  flex: 1;
  min-width: 0;
  width: 100%;
  height: 60rpx;
  background: #2176ff;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  padding: 0;
  box-sizing: border-box;
}
.ai-fab-dialog-sendicon {
  width: 32rpx;
  height: 32rpx;
}
.more-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(34, 118, 255, 0.10);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.more-dialog {
  width: 92vw;
  max-width: 700rpx;
  background: #fff;
  border-radius: 28rpx;
  box-shadow: 0 12rpx 48rpx #2176ff33;
  padding: 38rpx 32rpx 32rpx 32rpx;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
}
.more-dialog-title {
  font-size: 34rpx;
  color: #2176ff;
  font-weight: bold;
  margin-bottom: 18rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.more-dialog-close {
  font-size: 44rpx;
  color: #bbb;
  margin-left: 18rpx;
  cursor: pointer;
}
.more-dialog-list {
  flex: 1;
  min-height: 200rpx;
  max-height: 60vh;
  overflow-y: auto;
} 