const API = require('../../utils/api')

Page({
  data: {
    book: null,
    loading: true,
    isFavorited: false,
    recommend: [],
    showBuyDialog: false,
    quantity: 1,
    address: '',
    phone: ''
  },
  async onLoad(options) {
    const bookId = options.id;
    if (!bookId) {
      API.showError('书籍ID不能为空');
      wx.navigateBack();
      return;
    }

    await this.loadBookDetail(bookId);
    await this.checkFavoriteStatus(bookId);
    await this.addViewHistory(bookId);
    await this.loadRecommendBooks();
  },

  async loadBookDetail(bookId) {
    try {
      const result = await API.getBookDetail(bookId);
      if (result.success) {
        this.setData({
          book: result.data,
          loading: false
        });
      } else {
        API.showError(result.message);
        wx.navigateBack();
      }
    } catch (error) {
      console.error('获取书籍详情失败:', error);
      API.showError('获取书籍详情失败');
      wx.navigateBack();
    }
  },

  async checkFavoriteStatus(bookId) {
    try {
      const result = await API.getFavorites();
      if (result.success) {
        const isFavorited = result.data.some(fav => fav.bookId === bookId);
        this.setData({ isFavorited });
      }
    } catch (error) {
      console.error('检查收藏状态失败:', error);
    }
  },

  async addViewHistory(bookId) {
    try {
      await API.addViewHistory(bookId);
    } catch (error) {
      console.error('添加浏览记录失败:', error);
    }
  },

  async loadRecommendBooks() {
    try {
      const result = await API.getBookList({ page: 1, pageSize: 4 });
      if (result.success) {
        this.setData({ recommend: result.data.list });
      }
    } catch (error) {
      console.error('获取推荐书籍失败:', error);
    }
  },
  handleContactSeller() {
    const book = this.data.book;
    if (!book || !book.seller) {
      API.showError('卖家信息不存在');
      return;
    }

    wx.showModal({
      title: '联系卖家',
      content: `卖家：${book.seller.nickname}\n学校：${book.seller.school || '未填写'}`,
      confirmText: '发消息',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到聊天页面或发送消息
          API.showToast('消息功能开发中');
        }
      }
    });
  },

  async handleFavorite() {
    if (!this.data.book) return;

    try {
      if (this.data.isFavorited) {
        const result = await API.removeFavorite(this.data.book._id);
        if (result.success) {
          this.setData({ isFavorited: false });
          API.showSuccess('已取消收藏');
        } else {
          API.showError(result.message);
        }
      } else {
        const result = await API.addFavorite(this.data.book._id);
        if (result.success) {
          this.setData({ isFavorited: true });
          API.showSuccess('收藏成功');
        } else {
          API.showError(result.message);
        }
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
      API.showError('操作失败，请重试');
    }
  },

  handleBuy() {
    if (!this.data.book) return;

    // 检查是否是自己的书籍
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo._id === this.data.book.sellerId) {
      API.showError('不能购买自己的书籍');
      return;
    }

    // 检查书籍状态
    if (this.data.book.status !== 'on_sale') {
      API.showError('该书籍已下架或售出');
      return;
    }

    this.setData({ showBuyDialog: true });
  },

  closeBuyDialog() {
    this.setData({ showBuyDialog: false });
  },

  onQuantityChange(e) {
    const quantity = parseInt(e.detail.value) || 1;
    this.setData({ quantity: Math.max(1, Math.min(quantity, this.data.book.stock)) });
  },

  onAddressInput(e) {
    this.setData({ address: e.detail.value });
  },

  onPhoneInput(e) {
    this.setData({ phone: e.detail.value });
  },

  async confirmBuy() {
    if (!this.data.address.trim()) {
      API.showError('请填写收货地址');
      return;
    }

    if (!this.data.phone.trim()) {
      API.showError('请填写联系电话');
      return;
    }

    const phoneReg = /^1[3-9]\d{9}$/;
    if (!phoneReg.test(this.data.phone)) {
      API.showError('请输入正确的手机号');
      return;
    }

    try {
      const orderData = {
        bookId: this.data.book._id,
        quantity: this.data.quantity,
        address: this.data.address.trim(),
        phone: this.data.phone.trim()
      };

      const result = await API.createOrder(orderData);
      if (result.success) {
        API.showSuccess('订单创建成功');
        this.setData({ showBuyDialog: false });

        // 跳转到支付页面
        wx.navigateTo({
          url: `/pages/pay/pay?orderId=${result.data.orderId}&orderNo=${result.data.orderNo}`
        });
      } else {
        API.showError(result.message);
      }
    } catch (error) {
      console.error('创建订单失败:', error);
      API.showError('创建订单失败，请重试');
    }
  },

  onRecommendBookTap(e) {
    const bookId = e.currentTarget.dataset.id;
    wx.redirectTo({
      url: `/pages/book-detail/book-detail?id=${bookId}`
    });
  }
}) 