Page({
  data: {
    nickname: '',
    password: '',
    confirmPassword: '',
    phone: '',
    email: '',
    genderOptions: ['男', '女', '保密'],
    genderIndex: 0,
    loading: false
  },
  onNicknameInput(e) {
    this.setData({ nickname: e.detail.value });
  },
  onPasswordInput(e) {
    this.setData({ password: e.detail.value });
  },
  onConfirmPasswordInput(e) {
    this.setData({ confirmPassword: e.detail.value });
  },
  onPhoneInput(e) {
    this.setData({ phone: e.detail.value });
  },
  onEmailInput(e) {
    this.setData({ email: e.detail.value });
  },
  onGenderChange(e) {
    this.setData({ genderIndex: e.detail.value });
  },
  async onRegister() {
    // 表单验证
    if (!this.data.phone || !this.data.password || !this.data.confirmPassword || !this.data.nickname) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      })
      return
    }

    if (this.data.password !== this.data.confirmPassword) {
      wx.showToast({
        title: '两次密码输入不一致',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })
    try {
      const db = wx.cloud.database()
      
      // 检查手机号是否已注册
      const existUser = await db.collection('users').where({
        phone: this.data.phone
      }).get()

      if (existUser.data.length > 0) {
        wx.showToast({
          title: '该手机号已注册',
          icon: 'none'
        })
        return
      }

      // 获取微信用户信息
      const { result } = await wx.cloud.callFunction({
        name: 'login'
      })

      // 创建新用户
      const userData = {
        phone: this.data.phone,
        password: this.data.password,
        nickname: this.data.nickname,
        openid: result.openid,
        avatar: '',
        school: '',
        gender: '',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      }

      const res = await db.collection('users').add({
        data: userData
      })

      if (res._id) {
        // 注册成功，保存用户信息并跳转
        wx.setStorageSync('userInfo', userData)
        wx.showToast({
          title: '注册成功',
          icon: 'success'
        })
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/user-center/user-center'
          })
        }, 1500)
      }
    } catch (err) {
      console.error('注册失败：', err)
      wx.showToast({
        title: '注册失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },
  goLogin() {
    wx.navigateTo({ url: '/pages/login/login' });
  }
}); 