const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})
const db = cloud.database()
const _ = db.command

// 订单管理云函数
exports.main = async (event, context) => {
  const { type, data } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  switch (type) {
    // 创建订单
    case 'createOrder':
      try {
        const { bookId, quantity = 1, address, phone } = data

        // 数据验证
        if (!bookId || !address || !phone) {
          return {
            code: 400,
            message: '书籍ID、收货地址和联系电话不能为空'
          }
        }

        if (quantity <= 0) {
          return {
            code: 400,
            message: '购买数量必须大于0'
          }
        }

        // 手机号格式验证
        const phoneRegex = /^1[3-9]\d{9}$/
        if (!phoneRegex.test(phone)) {
          return {
            code: 400,
            message: '手机号格式不正确'
          }
        }

        // 检查书籍是否存在且在售
        const book = await db.collection('books').doc(bookId).get()
        if (!book.data) {
          return {
            code: 404,
            message: '书籍不存在'
          }
        }

        if (book.data.status !== 'on_sale') {
          return {
            code: 400,
            message: '书籍已下架或售出'
          }
        }

        if (book.data.sellerId === openid) {
          return {
            code: 400,
            message: '不能购买自己的书籍'
          }
        }

        // 检查库存
        if (book.data.stock < quantity) {
          return {
            code: 400,
            message: '库存不足'
          }
        }

        // 检查是否已有未完成的订单
        const existingOrder = await db.collection('orders')
          .where({
            bookId,
            buyerId: openid,
            status: _.in(['pending', 'paid', 'shipped'])
          })
          .get()

        if (existingOrder.data.length > 0) {
          return {
            code: 400,
            message: '您已有该书籍的未完成订单'
          }
        }

        // 生成订单号
        const orderNo = 'ORD' + Date.now() + Math.random().toString(36).substr(2, 4).toUpperCase()

        const orderData = {
          orderNo,
          bookId,
          buyerId: openid,
          sellerId: book.data.sellerId,
          quantity,
          totalPrice: book.data.price * quantity,
          address: address.trim(),
          phone,
          status: 'pending',
          paymentMethod: '',
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }

        const result = await db.collection('orders').add({
          data: orderData
        })

        // 发送订单创建通知
        await cloud.callFunction({
          name: 'message',
          data: {
            type: 'sendTradeMessage',
            data: {
              buyerId: openid,
              sellerId: book.data.sellerId,
              orderId: result._id,
              messageType: 'order_created'
            }
          }
        })

        return {
          code: 0,
          data: { orderId: result._id, orderNo },
          message: '订单创建成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '创建订单失败',
          error: error.message
        }
      }
    
    // 获取订单列表
    case 'getOrderList':
      try {
        const { page = 1, pageSize = 10, status, type: orderType } = data
        let query = {}
        
        if (orderType === 'buy') {
          query.buyerId = openid
        } else if (orderType === 'sell') {
          query.sellerId = openid
        } else {
          // 默认获取所有相关订单
          query = _.or([
            { buyerId: openid },
            { sellerId: openid }
          ])
        }
        
        if (status) {
          query.status = status
        }
        
        const result = await db.collection('orders')
          .where(query)
          .orderBy('createTime', 'desc')
          .skip((page - 1) * pageSize)
          .limit(pageSize)
          .get()
        
        // 获取订单关联的书籍信息
        const orders = []
        for (const order of result.data) {
          const book = await db.collection('books').doc(order.bookId).get()
          orders.push({
            ...order,
            book: book.data
          })
        }
        
        return {
          code: 0,
          data: {
            list: orders,
            total: orders.length,
            page,
            pageSize
          },
          message: '获取成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '获取订单失败',
          error: error.message
        }
      }
    
    // 更新订单状态
    case 'updateOrderStatus':
      try {
        const order = await db.collection('orders').doc(data.orderId).get()
        if (!order.data) {
          return {
            code: 404,
            message: '订单不存在'
          }
        }
        
        // 检查权限
        if (order.data.buyerId !== openid && order.data.sellerId !== openid) {
          return {
            code: 403,
            message: '无权限操作'
          }
        }
        
        const updateData = {
          status: data.status,
          updateTime: db.serverDate()
        }
        
        // 根据状态更新相应时间
        if (data.status === 'paid') {
          updateData.paymentTime = db.serverDate()
          updateData.paymentMethod = data.paymentMethod || 'wechat'
        } else if (data.status === 'shipped') {
          updateData.shipTime = db.serverDate()
        } else if (data.status === 'completed') {
          updateData.completeTime = db.serverDate()
          
          // 订单完成时，更新书籍状态为已售出
          await db.collection('books').doc(order.data.bookId).update({
            data: {
              status: 'sold',
              updateTime: db.serverDate()
            }
          })
        }
        
        await db.collection('orders').doc(data.orderId).update({
          data: updateData
        })
        
        return {
          code: 0,
          message: '状态更新成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '更新状态失败',
          error: error.message
        }
      }
    
    // 取消订单
    case 'cancelOrder':
      try {
        const order = await db.collection('orders').doc(data.orderId).get()
        if (!order.data) {
          return {
            code: 404,
            message: '订单不存在'
          }
        }
        
        // 只有买家可以取消订单，且只能在pending状态下取消
        if (order.data.buyerId !== openid) {
          return {
            code: 403,
            message: '无权限操作'
          }
        }
        
        if (order.data.status !== 'pending') {
          return {
            code: 400,
            message: '订单状态不允许取消'
          }
        }
        
        await db.collection('orders').doc(data.orderId).update({
          data: {
            status: 'cancelled',
            updateTime: db.serverDate()
          }
        })
        
        return {
          code: 0,
          message: '订单已取消'
        }
      } catch (error) {
        return {
          code: -1,
          message: '取消订单失败',
          error: error.message
        }
      }
    
    default:
      return {
        code: 404,
        message: '不支持的操作类型'
      }
  }
}
