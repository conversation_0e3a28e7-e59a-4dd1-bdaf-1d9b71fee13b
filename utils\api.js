/**
 * API工具类 - 统一云函数调用
 */

class API {
  /**
   * 调用云函数的通用方法
   * @param {string} name 云函数名称
   * @param {string} type 操作类型
   * @param {object} data 请求数据
   * @param {boolean} showLoading 是否显示加载提示
   * @returns {Promise} 返回Promise对象
   */
  static async call(name, type, data = {}, showLoading = true) {
    if (showLoading) {
      wx.showLoading({ title: '加载中...' });
    }

    try {
      const result = await wx.cloud.callFunction({
        name,
        data: { type, data }
      });

      if (showLoading) {
        wx.hideLoading();
      }

      // 统一处理返回结果
      if (result.result) {
        if (result.result.code === 0 || result.result.errCode === 0) {
          return {
            success: true,
            data: result.result.data || result.result,
            message: result.result.message || result.result.msg || '操作成功'
          };
        } else {
          return {
            success: false,
            message: result.result.message || result.result.msg || '操作失败',
            code: result.result.code || result.result.errCode
          };
        }
      } else {
        return {
          success: true,
          data: result,
          message: '操作成功'
        };
      }
    } catch (error) {
      if (showLoading) {
        wx.hideLoading();
      }
      
      console.error('API调用失败:', error);
      return {
        success: false,
        message: error.message || '网络错误，请重试',
        error
      };
    }
  }

  /**
   * 显示错误提示
   * @param {string} message 错误信息
   */
  static showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }

  /**
   * 显示成功提示
   * @param {string} message 成功信息
   */
  static showSuccess(message) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: 1500
    });
  }

  // ==================== 用户相关API ====================

  /**
   * 用户登录
   */
  static async login(loginData) {
    return await this.call('login', 'login', loginData);
  }

  /**
   * 获取用户信息
   */
  static async getUserInfo() {
    return await this.call('user', 'getUserInfo');
  }

  /**
   * 更新用户信息
   */
  static async updateUserInfo(userInfo) {
    return await this.call('user', 'updateUserInfo', userInfo);
  }

  /**
   * 学生认证
   */
  static async verifyStudent(verifyData) {
    return await this.call('user', 'verifyStudent', verifyData);
  }

  // ==================== 书籍相关API ====================

  /**
   * 获取书籍列表
   */
  static async getBookList(params = {}) {
    return await this.call('book', 'getBookList', params, false);
  }

  /**
   * 获取书籍详情
   */
  static async getBookDetail(bookId) {
    return await this.call('book', 'getBookDetail', { bookId });
  }

  /**
   * 发布书籍
   */
  static async publishBook(bookData) {
    return await this.call('book', 'publishBook', bookData);
  }

  /**
   * 更新书籍信息
   */
  static async updateBook(bookData) {
    return await this.call('book', 'updateBook', bookData);
  }

  /**
   * 删除书籍
   */
  static async deleteBook(bookId) {
    return await this.call('book', 'deleteBook', { bookId });
  }

  /**
   * 更新书籍状态
   */
  static async updateBookStatus(bookId, status) {
    return await this.call('book', 'updateBookStatus', { bookId, status });
  }

  // ==================== 收藏相关API ====================

  /**
   * 添加收藏
   */
  static async addFavorite(bookId) {
    return await this.call('user', 'addFavorite', { bookId });
  }

  /**
   * 取消收藏
   */
  static async removeFavorite(bookId) {
    return await this.call('user', 'removeFavorite', { bookId });
  }

  /**
   * 获取收藏列表
   */
  static async getFavorites() {
    return await this.call('user', 'getFavorites');
  }

  // ==================== 浏览记录API ====================

  /**
   * 添加浏览记录
   */
  static async addViewHistory(bookId) {
    return await this.call('user', 'addViewHistory', { bookId }, false);
  }

  /**
   * 获取浏览记录
   */
  static async getViewHistory() {
    return await this.call('user', 'getViewHistory');
  }

  // ==================== 订单相关API ====================

  /**
   * 创建订单
   */
  static async createOrder(orderData) {
    return await this.call('order', 'createOrder', orderData);
  }

  /**
   * 获取订单列表
   */
  static async getOrderList(params = {}) {
    return await this.call('order', 'getOrderList', params);
  }

  /**
   * 更新订单状态
   */
  static async updateOrderStatus(orderId, status) {
    return await this.call('order', 'updateOrderStatus', { orderId, status });
  }

  // ==================== 社区相关API ====================

  /**
   * 获取帖子列表
   */
  static async getPostList(params = {}) {
    return await this.call('community', 'getPostList', params, false);
  }

  /**
   * 创建帖子
   */
  static async createPost(postData) {
    return await this.call('community', 'createPost', postData);
  }

  /**
   * 获取评论列表
   */
  static async getCommentList(postId) {
    return await this.call('community', 'getCommentList', { postId }, false);
  }

  /**
   * 创建评论
   */
  static async createComment(commentData) {
    return await this.call('community', 'createComment', commentData);
  }

  /**
   * 点赞帖子
   */
  static async likePost(postId) {
    return await this.call('community', 'likePost', { postId }, false);
  }

  // ==================== 消息相关API ====================

  /**
   * 获取消息列表
   */
  static async getMessages(params = {}) {
    return await this.call('message', 'getMessages', params);
  }

  /**
   * 标记消息为已读
   */
  static async markMessageAsRead(messageId, markAll = false) {
    return await this.call('message', 'markAsRead', { messageId, markAll }, false);
  }

  /**
   * 删除消息
   */
  static async deleteMessage(messageId) {
    return await this.call('message', 'deleteMessage', { messageId });
  }

  /**
   * 发送消息
   */
  static async sendMessage(messageData) {
    return await this.call('message', 'sendMessage', messageData);
  }

  // ==================== 钱包相关API ====================

  /**
   * 获取钱包信息
   */
  static async getWalletInfo() {
    return await this.call('wallet', 'getWalletInfo');
  }

  /**
   * 充值
   */
  static async recharge(amount, paymentMethod = 'wechat') {
    return await this.call('wallet', 'recharge', { amount, paymentMethod });
  }

  /**
   * 提现
   */
  static async withdraw(withdrawData) {
    return await this.call('wallet', 'withdraw', withdrawData);
  }

  /**
   * 转账
   */
  static async transfer(transferData) {
    return await this.call('wallet', 'transfer', transferData);
  }

  /**
   * 获取交易记录
   */
  static async getTransactions(params = {}) {
    return await this.call('wallet', 'getTransactions', params);
  }

  // ==================== 文件上传API ====================

  /**
   * 上传图片到云存储
   */
  static async uploadImage(filePath, cloudPath) {
    try {
      wx.showLoading({ title: '上传中...' });

      const result = await wx.cloud.uploadFile({
        cloudPath,
        filePath
      });

      wx.hideLoading();

      return {
        success: true,
        data: { fileID: result.fileID },
        message: '上传成功'
      };
    } catch (error) {
      wx.hideLoading();
      console.error('图片上传失败:', error);
      return {
        success: false,
        message: '上传失败，请重试',
        error
      };
    }
  }

  // ==================== 用户注册API ====================

  /**
   * 用户注册
   */
  static async register(registerData) {
    return await this.call('user', 'register', registerData);
  }
}

module.exports = API;
