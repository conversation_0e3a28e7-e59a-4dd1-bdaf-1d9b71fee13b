const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})
const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { type, data } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  switch (type) {
    // 发布书籍
    case 'publishBook':
      try {
        const { title, price, degree, category, description, coverImage, images } = data

        // 数据验证
        if (!title || !price || !degree || !category) {
          return {
            code: 400,
            message: '书名、价格、成色和分类不能为空'
          }
        }

        if (price <= 0) {
          return {
            code: 400,
            message: '价格必须大于0'
          }
        }

        if (!coverImage) {
          return {
            code: 400,
            message: '请上传书籍封面图片'
          }
        }

        // 验证分类是否有效
        const validCategories = ['教材', '考研资料', '小说', '历史', '金融', '计算机', '其他']
        if (!validCategories.includes(category)) {
          return {
            code: 400,
            message: '无效的书籍分类'
          }
        }

        // 验证成色是否有效
        const validDegrees = ['全新', '九成新', '八成新', '七成新', '六成新', '五成新']
        if (!validDegrees.includes(degree)) {
          return {
            code: 400,
            message: '无效的书籍成色'
          }
        }

        const bookData = {
          title: title.trim(),
          author: data.author || '',
          publisher: data.publisher || '',
          isbn: data.isbn || '',
          coverImage,
          images: images || [],
          price: Number(price),
          originalPrice: data.originalPrice || 0,
          degree,
          category,
          description: description || '',
          stock: data.stock || 1,
          delivery: data.delivery || '均可',
          sellerId: openid,
          status: 'on_sale',
          viewCount: 0,
          favoriteCount: 0,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }

        const result = await db.collection('books').add({
          data: bookData
        })

        return {
          code: 0,
          data: { bookId: result._id },
          message: '发布成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '发布失败',
          error: error.message
        }
      }
    
    // 更新书籍信息
    case 'updateBook':
      try {
        // 检查是否是书籍所有者
        const book = await db.collection('books').doc(data.bookId).get()
        if (!book.data) {
          return {
            code: 404,
            message: '书籍不存在'
          }
        }

        if (book.data.sellerId !== openid) {
          return {
            code: 403,
            message: '无权限修改'
          }
        }

        const updateData = { ...data }
        delete updateData.bookId // 移除bookId字段
        updateData.updateTime = db.serverDate()

        await db.collection('books').doc(data.bookId).update({
          data: updateData
        })

        return {
          code: 0,
          message: '更新成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '更新失败',
          error: error.message
        }
      }
    
    // 删除书籍
    case 'deleteBook':
      const bookToDelete = await db.collection('books').doc(data.bookId).get()
      if (bookToDelete.data.sellerId !== openid) {
        return {
          code: 403,
          message: '无权限删除'
        }
      }
      return await db.collection('books').doc(data.bookId).remove()
    
    // 获取书籍详情
    case 'getBookDetail':
      try {
        const result = await db.collection('books').doc(data.bookId).get()
        if (!result.data) {
          return {
            code: 404,
            message: '书籍不存在'
          }
        }

        // 获取卖家信息
        const seller = await db.collection('users').doc(result.data.sellerId).get()
        const bookDetail = {
          ...result.data,
          seller: seller.data ? {
            nickname: seller.data.nickname,
            avatar: seller.data.avatar,
            school: seller.data.school,
            creditScore: seller.data.creditScore
          } : null
        }

        return {
          code: 0,
          data: bookDetail,
          message: '获取成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '获取失败',
          error: error.message
        }
      }

    // 获取书籍列表
    case 'getBookList':
      try {
        const { category, keyword, page = 1, pageSize = 10, sortBy = 'createTime' } = data
        let query = { status: 'on_sale' } // 只显示在售书籍

        if (category && category !== '全部') {
          query.category = category
        }

        if (keyword) {
          query.title = db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        }

        const result = await db.collection('books')
          .where(query)
          .orderBy(sortBy, 'desc')
          .skip((page - 1) * pageSize)
          .limit(pageSize)
          .get()

        return {
          code: 0,
          data: {
            list: result.data,
            total: result.data.length,
            page,
            pageSize
          },
          message: '获取成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '获取失败',
          error: error.message
        }
      }
    
    // 更新书籍状态（如售出）
    case 'updateBookStatus':
      const bookToUpdate = await db.collection('books').doc(data.bookId).get()
      if (bookToUpdate.data.sellerId !== openid) {
        return {
          code: 403,
          message: '无权限修改'
        }
      }
      return await db.collection('books').doc(data.bookId).update({
        data: {
          status: data.status,
          updateTime: db.serverDate()
        }
      })
  }
} 