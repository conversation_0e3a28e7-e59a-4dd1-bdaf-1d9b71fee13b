<view class="pay-container">
  <view class="pay-header">支付订单</view>
  <view class="pay-main">
    <view class="pay-book-card">
      <image class="pay-book-cover" src="{{book.cover}}" mode="aspectFit" />
      <view class="pay-book-info">
        <text class="pay-book-title">{{book.title}}</text>
        <text class="pay-book-author">{{book.author}}</text>
        <text class="pay-book-degree">{{book.degree}}</text>
      </view>
    </view>
    <view class="pay-method-section">
      <text class="pay-method-title">支付方式</text>
      <view class="pay-method-list">
        <label class="pay-method-item">
          <radio value="alipay" checked="{{payMethod==='alipay'}}" />
          <image src="/static/images/alipay.png" class="pay-method-icon" /> 支付宝支付
        </label>
        <label class="pay-method-item">
          <radio value="wechat" checked="{{payMethod==='wechat'}}" />
          <image src="/static/images/wechat.png" class="pay-method-icon" /> 微信支付
        </label>
      </view>
    </view>
    <button class="pay-confirm-btn" bindtap="onPay">确认支付 ￥{{book.price}}</button>
  </view>
</view> 