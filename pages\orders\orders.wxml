<view class="orders-container">
  <view class="orders-header">购买记录</view>
  <block wx:for="{{orders}}" wx:key="id">
    <view class="order-card">
      <text class="order-title">{{item.title}}</text>
      <text class="order-price">¥{{item.price}}</text>
      <text class="order-time">{{item.time}}</text>
    </view>
  </block>
</view>

<!-- 订单详情弹窗 -->
<view wx:if="{{showOrderDetailModal}}" class="modal-mask">
  <view class="modal">
    <view class="modal-title">订单详情<text class="modal-close" bindtap="onCloseOrderDetailModal">×</text></view>
    <view class="modal-content">
      <text>书名：{{orderDetail.title}}</text>
      <text>价格：¥{{orderDetail.price}}</text>
      <text>时间：{{orderDetail.time}}</text>
    </view>
    <button class="modal-btn" bindtap="onCloseOrderDetailModal">关闭</button>
  </view>
</view> 