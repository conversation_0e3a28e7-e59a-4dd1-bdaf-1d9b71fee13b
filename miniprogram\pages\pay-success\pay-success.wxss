.pay-success-container {
  min-height: 100vh;
  background: #f5f6fa;
  padding: 24rpx 0;
}
.pay-success-header {
  background: #219653;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  padding: 24rpx 0 24rpx 32rpx;
  border-radius: 16rpx 16rpx 0 0;
  margin-bottom: 24rpx;
}
.pay-success-main {
  margin: 0 auto;
  max-width: 900rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx #b3c6ff22;
  padding: 48rpx 40rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.pay-success-icon {
  width: 100rpx;
  height: 100rpx;
  background: #eafaf1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}
.pay-success-check {
  color: #219653;
  font-size: 64rpx;
  font-weight: bold;
}
.pay-success-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 18rpx;
}
.pay-success-order {
  color: #888;
  font-size: 22rpx;
  margin-bottom: 8rpx;
}
.pay-success-amount {
  color: #222;
  font-size: 24rpx;
  margin-bottom: 32rpx;
}
.pay-success-btns {
  display: flex;
  gap: 24rpx;
  margin-top: 12rpx;
}
.pay-success-btn {
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: bold;
  padding: 16rpx 36rpx;
  border: none;
  box-shadow: 0 2rpx 8rpx #b3c6ff22;
  transition: background 0.2s;
}
.pay-success-btn-outline {
  background: #fff;
  color: #2176ff;
  border: 2rpx solid #2176ff;
}
.pay-success-btn-blue {
  background: #2176ff;
  color: #fff;
} 