Page({
  data: {
    loginType: 'phone',
    phone: '',
    password: '',
    nickname: '',
    loading: false
  },

  onLoad() {
    // 检查是否已经登录
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      wx.redirectTo({
        url: '/pages/user-center/user-center'
      })
    }
  },

  onTabChange(e) {
    this.setData({ loginType: e.currentTarget.dataset.type });
  },

  onPhoneInput(e) {
    this.setData({ phone: e.detail.value });
  },

  onPasswordInput(e) {
    this.setData({ password: e.detail.value });
  },

  onNicknameInput(e) {
    this.setData({ nickname: e.detail.value });
  },

  async onPhoneLogin() {
    if (!this.data.phone || !this.data.password) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })
    try {
      const db = wx.cloud.database()
      const user = await db.collection('users').where({
        phone: this.data.phone,
        password: this.data.password
      }).get()

      if (user.data.length > 0) {
        const userInfo = user.data[0]
        wx.setStorageSync('userInfo', userInfo)
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/user-center/user-center'
          })
        }, 1500)
      } else {
        wx.showToast({
          title: '手机号或密码错误',
          icon: 'none'
        })
      }
    } catch (err) {
      console.error('登录失败：', err)
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  async onWechatLogin() {
    this.setData({ loading: true })
    try {
      const { result } = await wx.cloud.callFunction({
        name: 'login'
      })
      
      const db = wx.cloud.database()
      const user = await db.collection('users').where({
        openid: result.openid
      }).get()

      if (user.data.length > 0) {
        const userInfo = user.data[0]
        wx.setStorageSync('userInfo', userInfo)
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/user-center/user-center'
          })
        }, 1500)
      } else {
        // 新用户，跳转到注册页面
        wx.navigateTo({
          url: '/pages/register/register'
        })
      }
    } catch (err) {
      console.error('微信登录失败：', err)
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  async onNicknameLogin() {
    if (!this.data.nickname || !this.data.password) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })
    try {
      const db = wx.cloud.database()
      const user = await db.collection('users').where({
        nickname: this.data.nickname,
        password: this.data.password
      }).get()

      if (user.data.length > 0) {
        const userInfo = user.data[0]
        wx.setStorageSync('userInfo', userInfo)
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/user-center/user-center'
          })
        }, 1500)
      } else {
        wx.showToast({
          title: '昵称或密码错误',
          icon: 'none'
        })
      }
    } catch (err) {
      console.error('登录失败：', err)
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  goRegister() {
    wx.navigateTo({ url: '/pages/register/register' });
  }
}); 