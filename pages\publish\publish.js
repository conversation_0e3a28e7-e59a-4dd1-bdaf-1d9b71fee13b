const API = require('../../utils/api')

Page({
  data: {
    images: [],
    bookName: '',
    author: '',
    publisher: '',
    isbn: '',
    price: '',
    originalPrice: '',
    degreeOptions: ['全新', '九成新', '八成新', '七成新', '六成新', '五成新'],
    degreeIndex: 0,
    stock: 1,
    categoryOptions: ['教材', '考研资料', '小说', '历史', '金融', '计算机', '其他'],
    categoryIndex: 0,
    description: '',
    deliveryOptions: ['自取', '快递', '均可'],
    deliveryIndex: 2,
    loading: false
  },
  onChooseImage() {
    const maxCount = 9;
    const currentCount = this.data.images.length;

    if (currentCount >= maxCount) {
      API.showError('最多只能上传9张图片');
      return;
    }

    wx.chooseMedia({
      count: maxCount - currentCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFiles = res.tempFiles.map(file => file.tempFilePath);
        this.setData({
          images: this.data.images.concat(tempFiles)
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        API.showError('选择图片失败');
      }
    });
  },

  onRemoveImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.images;
    images.splice(index, 1);
    this.setData({ images });
  },

  onBookNameInput(e) {
    this.setData({ bookName: e.detail.value });
  },

  onAuthorInput(e) {
    this.setData({ author: e.detail.value });
  },

  onPublisherInput(e) {
    this.setData({ publisher: e.detail.value });
  },

  onIsbnInput(e) {
    this.setData({ isbn: e.detail.value });
  },

  onPriceInput(e) {
    this.setData({ price: e.detail.value });
  },

  onOriginalPriceInput(e) {
    this.setData({ originalPrice: e.detail.value });
  },

  onDegreeChange(e) {
    this.setData({ degreeIndex: e.detail.value });
  },

  onStockInput(e) {
    this.setData({ stock: parseInt(e.detail.value) || 1 });
  },

  onCategoryTap(e) {
    this.setData({ categoryIndex: e.currentTarget.dataset.index });
  },

  onDescriptionInput(e) {
    this.setData({ description: e.detail.value });
  },

  onDeliveryTap(e) {
    this.setData({ deliveryIndex: e.currentTarget.dataset.index });
  },
  async onPublish() {
    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    this.setData({ loading: true });

    try {
      // 上传图片
      const imageUrls = await this.uploadImages();

      // 构造书籍数据
      const bookData = {
        title: this.data.bookName.trim(),
        author: this.data.author.trim(),
        publisher: this.data.publisher.trim(),
        isbn: this.data.isbn.trim(),
        coverImage: imageUrls[0] || '',
        images: imageUrls,
        price: parseFloat(this.data.price),
        originalPrice: this.data.originalPrice ? parseFloat(this.data.originalPrice) : 0,
        degree: this.data.degreeOptions[this.data.degreeIndex],
        category: this.data.categoryOptions[this.data.categoryIndex],
        description: this.data.description.trim(),
        stock: this.data.stock,
        delivery: this.data.deliveryOptions[this.data.deliveryIndex]
      };

      // 调用发布书籍API
      const result = await API.publishBook(bookData);

      if (result.success) {
        API.showSuccess('发布成功');

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        API.showError(result.message);
      }
    } catch (error) {
      console.error('发布失败:', error);
      API.showError('发布失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  validateForm() {
    if (!this.data.bookName.trim()) {
      API.showError('请输入书名');
      return false;
    }

    if (!this.data.price || parseFloat(this.data.price) <= 0) {
      API.showError('请输入正确的价格');
      return false;
    }

    if (this.data.originalPrice && parseFloat(this.data.originalPrice) < parseFloat(this.data.price)) {
      API.showError('原价不能低于售价');
      return false;
    }

    if (this.data.stock < 1) {
      API.showError('库存数量至少为1');
      return false;
    }

    if (this.data.images.length === 0) {
      API.showError('请至少上传一张书籍图片');
      return false;
    }

    return true;
  },

  async uploadImages() {
    const imageUrls = [];

    for (let i = 0; i < this.data.images.length; i++) {
      const filePath = this.data.images[i];
      const cloudPath = `books/${Date.now()}_${i}.jpg`;

      try {
        const result = await API.uploadImage(filePath, cloudPath);
        if (result.success) {
          imageUrls.push(result.data.fileID);
        } else {
          throw new Error(result.message);
        }
      } catch (error) {
        console.error('上传图片失败:', error);
        throw new Error('图片上传失败');
      }
    }

    return imageUrls;
  }
}); 