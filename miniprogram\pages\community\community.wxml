<view class="community-container">
  <view class="community-header">
    <text class="community-title">书友交流社区</text>
    <button class="community-post-btn" bindtap="onShowPostModal">发帖</button>
  </view>
  <view class="community-list">
    <block wx:for="{{posts}}" wx:key="id">
      <view class="community-post-card">
        <image class="avatar" src="{{item.avatar}}" mode="aspectFill"/>
        <view class="post-main">
          <view class="post-header">
            <text class="nickname">{{item.nickname}}</text>
            <text class="time">{{item.time}}</text>
          </view>
          <text class="content">{{item.content}}</text>
          <view class="post-images" wx:if="{{item.images && item.images.length}}">
            <block wx:for="{{item.images}}" wx:key="img">
              <image class="post-image" src="{{img}}" mode="aspectFill" />
            </block>
          </view>
          <view class="post-actions">
            <button class="action-btn" bindtap="onLike" data-id="{{item.id}}">👍 {{item.likes}}</button>
            <button class="action-btn" bindtap="onComment" data-id="{{item.id}}">💬 评论</button>
            <button class="action-btn" bindtap="onShare" data-id="{{item.id}}">🔗 转发</button>
          </view>
          <view class="post-comments" wx:if="{{item.comments && item.comments.length}}">
            <block wx:for="{{item.comments}}" wx:key="cid">
              <view class="comment-item">
                <image class="comment-avatar" src="{{comment.avatar}}" mode="aspectFill"/>
                <text class="comment-nickname">{{comment.nickname}}：</text>
                <text class="comment-content">{{comment.content}}</text>
              </view>
            </block>
          </view>
          <view wx:if="{{commentingId === item.id}}" class="comment-input-bar">
            <input class="comment-input" placeholder="写评论..." value="{{commentInput}}" bindinput="onCommentInput"/>
            <button class="comment-send-btn" bindtap="onSendComment">发送</button>
          </view>
        </view>
      </view>
    </block>
  </view>
  <view wx:if="{{showPostModal}}" class="modal-mask">
    <view class="modal">
      <view class="modal-title">发布新帖<text class="modal-close" bindtap="onClosePostModal">×</text></view>
      <input class="modal-input" placeholder="昵称" value="{{postNickname}}" bindinput="onNicknameInput"/>
      <textarea class="modal-textarea" placeholder="分享你的读书心得..." value="{{postContent}}" bindinput="onContentInput"/>
      <view class="post-images">
        <block wx:for="{{postImages}}" wx:key="img">
          <image class="post-image" src="{{img}}" mode="aspectFill" />
        </block>
        <button class="action-btn" style="min-width:60rpx;" bindtap="onChooseImage">+</button>
      </view>
      <button class="modal-btn" bindtap="onPost">发布</button>
    </view>
  </view>
</view> 