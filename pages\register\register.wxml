<view class="register-container">
  <image src="/static/images/logo.png" class="register-logo" mode="aspectFit" />
  <view class="register-form">
    <input class="register-input" placeholder="请输入昵称" value="{{nickname}}" bindinput="onNicknameInput" />
    <input class="register-input" placeholder="请输入密码" password value="{{password}}" bindinput="onPasswordInput" />
    <input class="register-input" placeholder="请再次输入密码" password value="{{confirmPassword}}" bindinput="onConfirmPasswordInput" />
    <input class="register-input" placeholder="请输入手机号" maxlength="11" value="{{phone}}" bindinput="onPhoneInput" />
    <input class="register-input" placeholder="请输入邮箱" value="{{email}}" bindinput="onEmailInput" />
    <picker class="register-input" mode="selector" range="{{genderOptions}}" value="{{genderIndex}}" bindchange="onGenderChange">
      <view class="register-picker-text">{{genderOptions[genderIndex] || '请选择性别'}}</view>
    </picker>
    <button class="register-btn" bindtap="onRegister">注册</button>
  </view>
  <view class="register-link-row">
    <text>已有账号？</text>
    <text class="register-link" bindtap="goLogin">去登录</text>
  </view>
</view> 