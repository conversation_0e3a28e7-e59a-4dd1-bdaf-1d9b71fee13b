<view class="login-container">
  <image src="/static/images/logo.png" class="login-logo" mode="aspectFit" />
  <view class="login-tabs">
    <view class="login-tab {{loginType==='phone'?'active':''}}" bindtap="onTabChange" data-type="phone">手机号登录</view>
    <view class="login-tab {{loginType==='wechat'?'active':''}}" bindtap="onTabChange" data-type="wechat">微信登录</view>
    <view class="login-tab {{loginType==='nickname'?'active':''}}" bindtap="onTabChange" data-type="nickname">昵称密码登录</view>
  </view>
  <view wx:if="{{loginType==='phone'}}" class="login-form">
    <input class="login-input" placeholder="请输入手机号" maxlength="11" value="{{phone}}" bindinput="onPhoneInput" />
    <input class="login-input" placeholder="请输入密码" password value="{{password}}" bindinput="onPasswordInput" />
    <button class="login-btn" bindtap="onPhoneLogin">登录</button>
  </view>
  <view wx:if="{{loginType==='wechat'}}" class="login-form">
    <button class="login-btn wechat" open-type="getUserInfo" bindgetuserinfo="onWechatLogin">微信一键登录</button>
  </view>
  <view wx:if="{{loginType==='nickname'}}" class="login-form">
    <input class="login-input" placeholder="请输入昵称" value="{{nickname}}" bindinput="onNicknameInput" />
    <input class="login-input" placeholder="请输入密码" password value="{{password}}" bindinput="onPasswordInput" />
    <button class="login-btn" bindtap="onNicknameLogin">登录</button>
  </view>
  <view class="login-link-row">
    <text>还没有账号？</text>
    <text class="login-link" bindtap="goRegister">去注册</text>
  </view>
</view> 