Page({
  data: {
    book: {
      id: 1,
      cover: '/static/images/book1.jpg',
      title: '高等数学（第七版）',
      author: '同济大学数学系 编',
      price: 25.00,
      originPrice: 45.00,
      degree: '9成新',
      type: '教材',
      stock: 1,
      pub: '高等教育出版社',
      isbn: '9787040536668',
      pubDate: '2020年8月',
      desc: '本书是普通高等教育"十一五"国家级规划教材，是在第六版的基础上修订而成的。全书分上、下两册，上册包括函数与极限、导数与微分、微分中值定理与导数的应用、不定积分、定积分及其应用、微分方程等内容，下册包括空间解析几何与向量代数、多元函数微分法及其应用、重积分、曲线积分与曲面积分、无穷级数等内容。',
      delivery: '',
      imba: '',
      seller: {
        name: '张三',
        major: '计算机科学与技术学院',
        avatar: ''
      }
    },
    recommend: [
      {
        id: 2,
        cover: '/static/images/book2.jpg',
        title: '线性代数（第六版）',
        author: '王道论坛',
        price: 20.00,
        degree: '8成新',
        degreeType: 'purple',
        time: '2025-05-13 18:49',
        desc: '本书系统介绍了线性代数的基本理论和方法，包括行列式、矩阵、向量空间、特征值与特征向量等内容，适合理工科各专业学生使用。'
      },
      {
        id: 3,
        cover: '/static/images/book3.jpg',
        title: '概率论与数理统计',
        author: '李贤平',
        price: 22.00,
        degree: '9成新',
        degreeType: 'blue',
        time: '2025-05-13 18:49',
        desc: '本书内容涵盖概率论基础、随机变量及其分布、数字特征、大数定律与中心极限定理、数理统计基本方法等，适合高等院校相关专业使用。'
      }
    ],
    showCartDialog: false,
    cartDialogType: 'add', // add 或 cart
  },
  onLoad(options) {
    // 模拟从首页传递id，实际应请求API
    const id = Number(options.id);
    // 获取首页页面实例
    const pages = getCurrentPages();
    let book = null;
    if (pages.length > 1) {
      const indexPage = pages[pages.length - 2];
      const allBooks = [...(indexPage.data.hotBooks || []), ...(indexPage.data.newBooks || [])];
      book = allBooks.find(b => b.id === id);
    }
    if (book) {
      // 合并时保证所有字段有默认值
      const safeBook = Object.assign({}, this.data.book, book);
      if (!safeBook.seller) safeBook.seller = { name: '', major: '', avatar: '' };
      this.setData({ book: safeBook });
    }
  },
  handleContactSeller() {
    wx.showToast({ title: '联系卖家功能待实现', icon: 'none' })
  },
  handleAddCart() {
    // 模拟加入购物车
    this.setData({
      showCartDialog: true,
      cartDialogType: 'add'
    });
  },
  gotoCart() {
    this.setData({
      cartDialogType: 'cart'
    });
  },
  closeCartDialog() {
    this.setData({
      showCartDialog: false,
      cartDialogType: 'add'
    });
  },
  checkout() {
    wx.showToast({ title: '结算功能待实现', icon: 'none' })
    this.setData({ showCartDialog: false });
  },
  removeFromCart() {
    wx.showToast({ title: '已移除', icon: 'success' })
    this.setData({ showCartDialog: false });
  },
  handleBuy() {
    // 跳转到支付页面，传递书籍信息
    const book = this.data.book;
    wx.navigateTo({
      url: `/pages/pay/pay?cover=${encodeURIComponent(book.cover)}&title=${encodeURIComponent(book.title)}&author=${encodeURIComponent(book.author)}&degree=${encodeURIComponent(book.degree)}&price=${book.price}`
    });
  }
}) 