.ai-assistant {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.message-list {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.message {
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
}

.message.user {
  align-items: flex-end;
}

.message.ai {
  align-items: flex-start;
}

.message-content {
  max-width: 80%;
  padding: 20rpx;
  border-radius: 10rpx;
  word-break: break-all;
}

.user .message-content {
  background-color: #007AFF;
  color: white;
}

.ai .message-content {
  background-color: white;
  color: #333;
}

.reasoning {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 10rpx;
  border-radius: 6rpx;
}

.input-area {
  padding: 20rpx;
  background-color: white;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
}

.input {
  flex: 1;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  margin-right: 20rpx;
}

.send-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background-color: #007AFF;
  color: white;
  border-radius: 36rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.send-btn[disabled] {
  background-color: #ccc;
}

.clear-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background-color: #ff3b30;
  color: white;
  border-radius: 36rpx;
  font-size: 28rpx;
} 