const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})
const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { type, data } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  switch (type) {
    // 发布帖子
    case 'createPost':
      return await db.collection('posts').add({
        data: {
          ...data,
          authorId: openid,
          createTime: db.serverDate(),
          updateTime: db.serverDate(),
          likeCount: 0,
          commentCount: 0
        }
      })
    
    // 更新帖子
    case 'updatePost':
      const post = await db.collection('posts').doc(data.postId).get()
      if (post.data.authorId !== openid) {
        return {
          code: 403,
          message: '无权限修改'
        }
      }
      return await db.collection('posts').doc(data.postId).update({
        data: {
          ...data,
          updateTime: db.serverDate()
        }
      })
    
    // 删除帖子
    case 'deletePost':
      const postToDelete = await db.collection('posts').doc(data.postId).get()
      if (postToDelete.data.authorId !== openid) {
        return {
          code: 403,
          message: '无权限删除'
        }
      }
      // 删除帖子相关的评论
      await db.collection('comments').where({
        postId: data.postId
      }).remove()
      return await db.collection('posts').doc(data.postId).remove()
    
    // 获取帖子列表
    case 'getPostList':
      const { page = 1, pageSize = 10 } = data
      return await db.collection('posts')
        .orderBy('createTime', 'desc')
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .get()
    
    // 获取帖子详情
    case 'getPostDetail':
      return await db.collection('posts').doc(data.postId).get()
    
    // 发布评论
    case 'createComment':
      const commentResult = await db.collection('comments').add({
        data: {
          ...data,
          authorId: openid,
          createTime: db.serverDate(),
          likeCount: 0
        }
      })
      // 更新帖子评论数
      await db.collection('posts').doc(data.postId).update({
        data: {
          commentCount: _.inc(1)
        }
      })
      return commentResult
    
    // 删除评论
    case 'deleteComment':
      const comment = await db.collection('comments').doc(data.commentId).get()
      if (comment.data.authorId !== openid) {
        return {
          code: 403,
          message: '无权限删除'
        }
      }
      const deleteResult = await db.collection('comments').doc(data.commentId).remove()
      // 更新帖子评论数
      await db.collection('posts').doc(comment.data.postId).update({
        data: {
          commentCount: _.inc(-1)
        }
      })
      return deleteResult
    
    // 获取评论列表
    case 'getCommentList':
      return await db.collection('comments')
        .where({
          postId: data.postId
        })
        .orderBy('createTime', 'desc')
        .get()
    
    // 点赞帖子
    case 'likePost':
      const likeResult = await db.collection('posts').doc(data.postId).update({
        data: {
          likeCount: _.inc(1)
        }
      })
      // 记录点赞信息
      await db.collection('likes').add({
        data: {
          userId: openid,
          postId: data.postId,
          createTime: db.serverDate()
        }
      })
      return likeResult
    
    // 取消点赞
    case 'unlikePost':
      const unlikeResult = await db.collection('posts').doc(data.postId).update({
        data: {
          likeCount: _.inc(-1)
        }
      })
      // 删除点赞记录
      await db.collection('likes')
        .where({
          userId: openid,
          postId: data.postId
        })
        .remove()
      return unlikeResult
  }
} 