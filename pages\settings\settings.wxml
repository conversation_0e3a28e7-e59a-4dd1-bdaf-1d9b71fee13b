<view class="settings-container">
  <view class="settings-header">设置</view>
  <view class="settings-list">
    <view class="settings-item" bindtap="onChangePwd">
      <image class="settings-icon" src="/static/images/lock.png" /> 修改密码
    </view>
    <view class="settings-item" bindtap="onBindPhone">
      <image class="settings-icon" src="/static/images/phone.png" /> 绑定手机号
    </view>
    <view class="settings-item" bindtap="onBindStudent">
      <image class="settings-icon" src="/static/images/idcard.png" /> 绑定学号
    </view>
  </view>
</view>

<!-- 修改密码弹窗 -->
<view wx:if="{{showPwdModal}}" class="modal-mask">
  <view class="modal settings-modal">
    <view class="modal-title settings-modal-title">修改密码<text class="modal-close" bindtap="onClosePwdModal">×</text></view>
    <view class="input-row"><image class="input-icon" src="/static/images/lock.png" /><input class="modal-input" placeholder="原密码" password value="{{oldPwd}}" bindinput="onOldPwdInput"/></view>
    <view class="input-row"><image class="input-icon" src="/static/images/lock.png" /><input class="modal-input" placeholder="新密码" password value="{{newPwd}}" bindinput="onNewPwdInput"/></view>
    <view class="input-row"><image class="input-icon" src="/static/images/lock.png" /><input class="modal-input" placeholder="确认新密码" password value="{{confirmPwd}}" bindinput="onConfirmPwdInput"/></view>
    <button class="modal-btn settings-modal-btn" bindtap="onSavePwd">保存</button>
  </view>
</view>

<!-- 绑定手机号弹窗 -->
<view wx:if="{{showPhoneModal}}" class="modal-mask">
  <view class="modal settings-modal">
    <view class="modal-title settings-modal-title">绑定手机号<text class="modal-close" bindtap="onClosePhoneModal">×</text></view>
    <view class="input-row"><image class="input-icon" src="/static/images/phone.png" /><input class="modal-input" placeholder="请输入手机号" value="{{phone}}" bindinput="onPhoneInput"/></view>
    <button class="modal-btn settings-modal-btn" bindtap="onSavePhone">保存</button>
  </view>
</view>

<!-- 绑定学号弹窗 -->
<view wx:if="{{showStudentModal}}" class="modal-mask">
  <view class="modal settings-modal">
    <view class="modal-title settings-modal-title">绑定学号<text class="modal-close" bindtap="onCloseStudentModal">×</text></view>
    <view class="input-row"><image class="input-icon" src="/static/images/idcard.png" /><input class="modal-input" placeholder="请输入学号" value="{{studentId}}" bindinput="onStudentInput"/></view>
    <button class="modal-btn settings-modal-btn" style="margin-bottom:18rpx;" bindtap="onUnbindStudent">解除绑定</button>
    <button class="modal-btn settings-modal-btn" style="margin-bottom:18rpx;" bindtap="onRebindStudent">重新绑定</button>
    <button class="modal-btn settings-modal-btn" bindtap="onSaveStudent">保存</button>
  </view>
</view> 