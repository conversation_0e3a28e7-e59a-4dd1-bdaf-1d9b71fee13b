.tabbar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  height: 110rpx;
  background: #fff;
  border-top-left-radius: 36rpx;
  border-top-right-radius: 36rpx;
  box-shadow: 0 -4rpx 24rpx 0 rgba(0,0,0,0.06);
  padding-bottom: env(safe-area-inset-bottom);
}
.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 24rpx;
  color: #bdbdbd;
  position: relative;
}
.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 4rpx;
  filter: grayscale(1) brightness(1.2);
  /* 默认灰色 */
}
.tabbar-text {
  font-size: 22rpx;
  color: #bdbdbd;
  filter: grayscale(1) brightness(1.2);
}
.tabbar-icon.active,
.tabbar-text.active {
  color: #2176ff;
  filter: none;
}
.tabbar-icon.inactive,
.tabbar-text.inactive {
  color: #bdbdbd;
  filter: grayscale(1) brightness(1.2);
}
.tabbar-item-add {
  position: relative;
  top: -32rpx;
  z-index: 2;
}
.tabbar-icon-add.tabbar-icon-publish {
  width: 72rpx;
  height: 72rpx;
  background: #2176ff;
  border-radius: 50%;
  box-shadow: 0 4rpx 16rpx 0 rgba(33,118,255,0.18);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  padding: 0;
  filter: none;
}
.tabbar-plus {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  line-height: 72rpx;
  text-align: center;
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tabbar-text-publish {
  color: #2176ff;
  font-weight: bold;
  font-size: 24rpx;
  margin-top: 2rpx;
  filter: none;
}
.always-blue {
  color: #2176ff !important;
  filter: none !important;
} 