<view class="eco-container">
  <!-- 顶部轮播图 -->
  <swiper class="eco-swiper" indicator-dots="true" autoplay="true" interval="4000" duration="500">
    <block wx:for="{{banners}}" wx:key="img">
      <swiper-item>
        <image src="{{item.img}}" class="eco-swiper-img" mode="aspectFill" />
        <view class="eco-swiper-mask">
          <text class="eco-swiper-slogan">{{item.slogan}}</text>
        </view>
      </swiper-item>
    </block>
  </swiper>
  <!-- 积分与捐赠按钮 -->
  <view class="eco-points-bar">
    <image src="/static/images/leaf.png" class="eco-points-icon" />
    <text class="eco-points-label">我的绿色积分：</text>
    <text class="eco-points-value">{{points}}</text>
    <button class="eco-donate-btn" bindtap="onShowDonate">去捐赠</button>
  </view>
  <!-- 捐赠流程弹窗 -->
  <view wx:if="{{showDonateDialog}}" class="eco-donate-mask">
    <view class="eco-donate-dialog">
      <view class="eco-donate-title">捐赠书籍<text class="eco-donate-close" bindtap="onCloseDonate">×</text></view>
      <view class="eco-donate-upload-row">
        <text class="eco-donate-label">上传书籍图片</text>
        <view class="eco-donate-img-list">
          <view class="eco-donate-img-upload" bindtap="onChooseDonateImg">
            <text class="eco-donate-img-plus">+</text>
          </view>
          <block wx:for="{{donateImgs}}" wx:key="index">
            <image src="{{item}}" class="eco-donate-img-thumb" mode="aspectFill" />
          </block>
        </view>
      </view>
      <input class="eco-donate-input" placeholder="书名" value="{{donateBookName}}" bindinput="onDonateBookNameInput" />
      <input class="eco-donate-input" placeholder="作者" value="{{donateAuthor}}" bindinput="onDonateAuthorInput" />
      <picker class="eco-donate-input" mode="selector" range="{{schoolList}}" value="{{donateSchoolIndex}}" bindchange="onDonateSchoolChange">
        <view class="eco-donate-picker-text">{{schoolList[donateSchoolIndex] || '选择捐赠学校'}}</view>
      </picker>
      <button class="eco-donate-confirm" bindtap="onDonateConfirm">确认捐赠</button>
    </view>
  </view>
  <!-- 书籍捐赠数据可视化 -->
  <view class="eco-section eco-trace-section">
    <text class="eco-section-title">书籍捐赠数据可视化</text>
    <view class="eco-chart-group">
      <view class="eco-chart-box">
        <text class="eco-chart-title">各学校捐赠数量（柱状图）</text>
        <ec-canvas id="barChart" canvas-id="barChart" ec="{{barChart}}" style="width: 100%; height: 320rpx;"></ec-canvas>
      </view>
      <view class="eco-chart-box">
        <text class="eco-chart-title">分配均匀度（饼状图）</text>
        <ec-canvas id="pieChart" canvas-id="pieChart" ec="{{pieChart}}" style="width: 100%; height: 320rpx;"></ec-canvas>
      </view>
    </view>
  </view>
  <!-- 感谢信卡片 -->
  <view class="eco-section eco-thank-section">
    <text class="eco-section-title">来自农村孩子的感谢信</text>
    <block wx:for="{{thanksList}}" wx:key="id">
      <view class="eco-thank-card">
        <view class="eco-thank-header">
          <image src="{{item.avatar}}" class="eco-thank-avatar" />
          <text class="eco-thank-name">{{item.name}}</text>
          <text class="eco-thank-school">{{item.school}}</text>
        </view>
        <text class="eco-thank-content">{{item.content}}</text>
        <view class="eco-thank-footer">
          <text class="eco-thank-time">{{item.time}}</text>
          <button class="eco-reply-btn" bindtap="onReply" data-index="{{index}}">回复</button>
        </view>
      </view>
    </block>
  </view>
  <!-- 回复弹窗 -->
  <view wx:if="{{showReplyDialog}}" class="eco-reply-mask">
    <view class="eco-reply-dialog">
      <view class="eco-reply-title">回复感谢信<text class="eco-reply-close" bindtap="onCloseReply">×</text></view>
      <textarea class="eco-reply-textarea" placeholder="写下你的鼓励与祝福..." value="{{replyInput}}" bindinput="onReplyInput" />
      <button class="eco-reply-send" bindtap="onSendReply">发送</button>
    </view>
  </view>
</view>

<tabbar active="1" /> 