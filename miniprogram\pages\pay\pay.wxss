.pay-container {
  min-height: 100vh;
  background: #f5f6fa;
}
.pay-header {
  background: #2176ff;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  padding: 24rpx 0 24rpx 32rpx;
  border-radius: 16rpx 16rpx 0 0;
  margin-bottom: 24rpx;
}
.pay-main {
  margin: 0 auto;
  max-width: 900rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx #b3c6ff22;
  padding: 32rpx 40rpx 40rpx 40rpx;
}
.pay-book-card {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx 24rpx 24rpx 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx #e5e7eb22;
}
.pay-book-cover {
  width: 120rpx;
  height: 160rpx;
  border-radius: 8rpx;
  background: #f3f4f6;
  object-fit: contain;
  margin-right: 24rpx;
}
.pay-book-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.pay-book-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 8rpx;
}
.pay-book-author {
  font-size: 22rpx;
  color: #888;
  margin-bottom: 4rpx;
}
.pay-book-degree {
  font-size: 20rpx;
  color: #2176ff;
  background: #dbeafe;
  border-radius: 8rpx;
  padding: 2rpx 12rpx;
  display: inline-block;
}
.pay-method-section {
  margin-bottom: 32rpx;
}
.pay-method-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 12rpx;
  display: block;
}
.pay-method-list {
  display: flex;
  gap: 32rpx;
  margin-top: 8rpx;
}
.pay-method-item {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #333;
  background: #f3f4f6;
  border-radius: 12rpx;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
}
.pay-method-icon {
  width: 32rpx;
  height: 32rpx;
  margin: 0 8rpx 0 4rpx;
}
.pay-confirm-btn {
  background: #2176ff;
  color: #fff;
  border-radius: 32rpx;
  font-size: 30rpx;
  font-weight: bold;
  padding: 20rpx 0;
  margin-top: 24rpx;
  width: 100%;
  box-shadow: 0 2rpx 8rpx #b3c6ff44;
  border: none;
} 