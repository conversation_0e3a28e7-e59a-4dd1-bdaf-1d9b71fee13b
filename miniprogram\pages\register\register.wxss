.register-container { min-height: 100vh; background: linear-gradient(180deg, #f5faff 0%, #e6f0ff 100%); display: flex; flex-direction: column; align-items: center; justify-content: flex-start; padding-top: 120rpx; }
.register-logo { width: 240rpx; height: 240rpx; margin-bottom: 48rpx; display: block; border-radius: 32rpx; box-shadow: 0 8rpx 32rpx #2176ff22; background: #fff; }
.register-form { width: 82vw; max-width: 540rpx; display: flex; flex-direction: column; gap: 36rpx; margin-bottom: 36rpx; }
.register-input { height: 68rpx; border-radius: 22rpx; border: 1.5rpx solid #e5e7eb; background: #fff; font-size: 30rpx; color: #222; padding: 0 28rpx; margin-bottom: 0; box-shadow: 0 2rpx 8rpx #2176ff11; }
.register-picker-text { color: #888; font-size: 30rpx; line-height: 68rpx; }
.register-btn { height: 68rpx; background: linear-gradient(90deg, #2176ff 0%, #5faaff 100%); color: #fff; border-radius: 22rpx; font-size: 32rpx; font-weight: bold; border: none; margin-top: 16rpx; box-shadow: 0 4rpx 16rpx #2176ff22; }
.register-link-row { display: flex; align-items: center; justify-content: center; gap: 8rpx; margin-top: 22rpx; }
.register-link { color: #2176ff; font-size: 30rpx; cursor: pointer; }
.register-tab { font-size: 24rpx; } 