Page({
  data: {
    showPwdModal: false,
    showPhoneModal: false,
    showStudentModal: false,
    oldPwd: '', newPwd: '', confirmPwd: '',
    phone: '', studentId: ''
  },
  onChangePwd() { this.setData({ showPwdModal: true }); },
  onClosePwdModal() { this.setData({ showPwdModal: false, oldPwd: '', newPwd: '', confirmPwd: '' }); },
  onOldPwdInput(e) { this.setData({ oldPwd: e.detail.value }); },
  onNewPwdInput(e) { this.setData({ newPwd: e.detail.value }); },
  onConfirmPwdInput(e) { this.setData({ confirmPwd: e.detail.value }); },
  async onSavePwd() {
    if (!this.data.oldPwd || !this.data.newPwd || !this.data.confirmPwd) return wx.showToast({ title: '请填写完整', icon: 'none' });
    if (this.data.newPwd !== this.data.confirmPwd) return wx.showToast({ title: '两次密码不一致', icon: 'none' });
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo._id) return wx.showToast({ title: '用户信息异常', icon: 'none' });
    if (userInfo.password && userInfo.password !== this.data.oldPwd) return wx.showToast({ title: '原密码错误', icon: 'none' });
    wx.showLoading({ title: '保存中...' });
    try {
      const db = wx.cloud.database();
      await db.collection('users').doc(userInfo._id).update({
        data: { password: this.data.newPwd }
      });
      wx.setStorageSync('userInfo', { ...userInfo, password: this.data.newPwd });
      this.setData({ showPwdModal: false, oldPwd: '', newPwd: '', confirmPwd: '' });
      wx.showToast({ title: '修改成功', icon: 'success' });
    } catch (e) {
      wx.showToast({ title: '保存失败', icon: 'none' });
    } finally {
      wx.hideLoading();
    }
  },
  onBindPhone() { this.setData({ showPhoneModal: true }); },
  onClosePhoneModal() { this.setData({ showPhoneModal: false, phone: '' }); },
  onPhoneInput(e) { this.setData({ phone: e.detail.value }); },
  async onSavePhone() {
    if (!this.data.phone) return wx.showToast({ title: '请输入手机号', icon: 'none' });
    const phoneReg = /^1[3-9]\d{9}$/;
    if (!phoneReg.test(this.data.phone)) return wx.showToast({ title: '手机号格式不正确', icon: 'none' });
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo._id) return wx.showToast({ title: '用户信息异常', icon: 'none' });
    wx.showLoading({ title: '保存中...' });
    try {
      const db = wx.cloud.database();
      await db.collection('users').doc(userInfo._id).update({
        data: { phone: this.data.phone }
      });
      wx.setStorageSync('userInfo', { ...userInfo, phone: this.data.phone });
      this.setData({ showPhoneModal: false, phone: '' });
      wx.showToast({ title: '绑定成功', icon: 'success' });
    } catch (e) {
      wx.showToast({ title: '保存失败', icon: 'none' });
    } finally {
      wx.hideLoading();
    }
  },
  onBindStudent() { this.setData({ showStudentModal: true }); },
  onCloseStudentModal() { this.setData({ showStudentModal: false, studentId: '' }); },
  onStudentInput(e) { this.setData({ studentId: e.detail.value }); },
  async onSaveStudent() {
    if (!this.data.studentId) return wx.showToast({ title: '请输入学号', icon: 'none' });
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo._id) return wx.showToast({ title: '用户信息异常', icon: 'none' });
    wx.showLoading({ title: '保存中...' });
    try {
      const db = wx.cloud.database();
      await db.collection('users').doc(userInfo._id).update({
        data: { studentId: this.data.studentId }
      });
      wx.setStorageSync('userInfo', { ...userInfo, studentId: this.data.studentId });
      this.setData({ showStudentModal: false, studentId: this.data.studentId });
      wx.setStorageSync('studentAuth', true);
      wx.showToast({ title: '绑定成功', icon: 'success' });
    } catch (e) {
      wx.showToast({ title: '保存失败', icon: 'none' });
    } finally {
      wx.hideLoading();
    }
  },
  onUnbindStudent() {
    this.setData({ studentId: '', showStudentModal: false });
    try {
      wx.setStorageSync('studentAuth', false);
    } catch (e) {}
    wx.showToast({ title: '已解除绑定', icon: 'success' });
    // 可选：通知个人中心页面刷新
  },
  onRebindStudent() {
    this.setData({ studentId: '' });
  }
}) 