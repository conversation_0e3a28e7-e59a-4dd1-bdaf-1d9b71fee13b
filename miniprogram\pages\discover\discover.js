const echarts = require('../../components/ec-canvas/echarts');

const chartColors = ['#2ecc40', '#ffb347', '#4f8cff', '#ff6f91', '#a084e8'];

function initBarChart(canvas, width, height, dpr, schoolData) {
  const chart = echarts.init(canvas, null, { width, height, devicePixelRatio: dpr });
  canvas.setChart(chart);
  const option = {
    color: chartColors,
    tooltip: { trigger: 'axis' },
    grid: { left: '6%', right: '6%', bottom: '10%', top: '12%', containLabel: true },
    xAxis: {
      type: 'category',
      data: schoolData.map(item => item.name),
      axisLabel: { color: '#217a2b', fontWeight: 'bold', fontSize: 14 }
    },
    yAxis: {
      type: 'value',
      axisLabel: { color: '#888' }
    },
    series: [{
      name: '捐赠数量',
      type: 'bar',
      barWidth: '40%',
      data: schoolData.map(item => item.value),
      itemStyle: {
        borderRadius: [8, 8, 0, 0],
        shadowColor: '#e5e7eb',
        shadowBlur: 8
      },
      label: {
        show: true,
        position: 'top',
        color: '#217a2b',
        fontWeight: 'bold',
        fontSize: 14
      }
    }]
  };
  chart.setOption(option);
  return chart;
}

function initPieChart(canvas, width, height, dpr, schoolData) {
  const chart = echarts.init(canvas, null, { width, height, devicePixelRatio: dpr });
  canvas.setChart(chart);
  const option = {
    color: chartColors,
    tooltip: { trigger: 'item', formatter: '{b}: {c}本 ({d}%)' },
    legend: {
      orient: 'vertical',
      left: 'right',
      top: 'center',
      textStyle: { color: '#217a2b', fontSize: 13 }
    },
    series: [{
      name: '分配均匀度',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}\n{d}%'
      },
      labelLine: { show: true },
      data: schoolData
    }]
  };
  chart.setOption(option);
  return chart;
}

Page({
  data: {
    banners: [
      { img: '/static/images/eco1.jpg', slogan: '绿色环保 书籍回收 让爱心传递' },
      { img: '/static/images/eco2.jpg', slogan: '旧书新生 让知识点亮乡村' },
      { img: '/static/images/eco3.jpg', slogan: '捐一本书 温暖一颗心' }
    ],
    traces: [
      { school: '贵州省望谟县民族小学', dest: '贵州省黔西南州望谟县' },
      { school: '云南省昭通市鲁甸县第一小学', dest: '云南省昭通市鲁甸县' },
      { school: '四川省凉山州布拖县民族小学', dest: '四川省凉山州布拖县' },
      { school: '甘肃省定西市安定区李家堡小学', dest: '甘肃省定西市安定区' },
      { school: '广西壮族自治区百色市田阳县第一小学', dest: '广西百色市田阳县' }
    ],
    thanksList: [
      { id: 1, avatar: '/static/images/kid1.png', name: '小明', school: '望谟县民族小学', content: '谢谢您的捐赠，我一定会好好学习，将来回报社会！', time: '2天前' },
      { id: 2, avatar: '/static/images/kid2.png', name: '小红', school: '鲁甸县第一小学', content: '感谢您的爱心书籍，我很喜欢这些故事书！', time: '3天前' },
      { id: 3, avatar: '/static/images/kid3.png', name: '小军', school: '李家堡小学', content: '谢谢叔叔阿姨送来的课外书，我会和同学们一起分享！', time: '1天前' },
      { id: 4, avatar: '/static/images/kid4.png', name: '小芳', school: '田阳县第一小学', content: '有了新书，我的学习更有动力啦，谢谢您！', time: '4天前' }
    ],
    points: 120,
    showReplyDialog: false,
    replyInput: '',
    replyToIndex: null,
    showDonateDialog: false,
    donateImgs: [],
    donateBookName: '',
    donateAuthor: '',
    donateSchoolIndex: 0,
    schoolList: ['贵州省望谟县民族小学', '云南省昭通市鲁甸县第一小学', '四川省凉山州布拖县民族小学', '甘肃省定西市安定区李家堡小学', '广西壮族自治区百色市田阳县第一小学'],
    traceSteps: [
      { city: '广州', time: '6月1日 09:00' },
      { city: '长沙', time: '6月2日 12:00' },
      { city: '贵阳', time: '6月3日 15:00' },
      { city: '望谟县民族小学', time: '6月4日 10:00' }
    ],
    activeTabbarIndex: 1,
    barChart: {},
    pieChart: {}
  },
  onReply(e) {
    this.setData({ showReplyDialog: true, replyInput: '', replyToIndex: e.currentTarget.dataset.index });
  },
  onCloseReply() {
    this.setData({ showReplyDialog: false });
  },
  onReplyInput(e) {
    this.setData({ replyInput: e.detail.value });
  },
  onSendReply() {
    if (!this.data.replyInput.trim()) {
      wx.showToast({ title: '请输入内容', icon: 'none' });
      return;
    }
    wx.showToast({ title: '回复成功', icon: 'success' });
    this.setData({ showReplyDialog: false, replyInput: '' });
  },
  onShowDonate() {
    this.setData({ showDonateDialog: true, donateImgs: [], donateBookName: '', donateAuthor: '', donateSchoolIndex: 0 });
  },
  onCloseDonate() {
    this.setData({ showDonateDialog: false });
  },
  onChooseDonateImg() {
    const that = this;
    wx.chooseImage({
      count: 6 - that.data.donateImgs.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success(res) {
        that.setData({ donateImgs: that.data.donateImgs.concat(res.tempFilePaths) });
      }
    });
  },
  onDonateBookNameInput(e) { this.setData({ donateBookName: e.detail.value }); },
  onDonateAuthorInput(e) { this.setData({ donateAuthor: e.detail.value }); },
  onDonateSchoolChange(e) { this.setData({ donateSchoolIndex: e.detail.value }); },
  onDonateConfirm() {
    if (!this.data.donateBookName.trim() || !this.data.donateAuthor.trim() || this.data.donateImgs.length === 0) {
      wx.showToast({ title: '请填写完整信息', icon: 'none' });
      return;
    }
    // 模拟捐赠成功，积分+10
    this.setData({
      points: this.data.points + 10,
      showDonateDialog: false,
      donateImgs: [],
      donateBookName: '',
      donateAuthor: ''
    });
    wx.showToast({ title: '捐赠成功，积分+10', icon: 'success' });
  },
  onTabbarTap(e) {
    const index = Number(e.currentTarget.dataset.index);
    if (index === this.data.activeTabbarIndex) return;
    this.setData({ activeTabbarIndex: index });
    if (index === 0) {
      wx.redirectTo({ url: '/pages/index/index' });
    } else if (index === 4) {
      wx.redirectTo({ url: '/pages/user-center/user-center' });
    } else {
      wx.showToast({ title: '功能开发中', icon: 'none' });
    }
  },
  onTabbarPublish() {
    wx.navigateTo({ url: '/pages/publish/publish' });
  },
  onTabbarMessage() {
    this.setData({ activeTabbarIndex: 3 });
    wx.redirectTo({ url: '/pages/message/message' });
  },
  onTabbarDiscover() {
    this.setData({ activeTabbarIndex: 1 });
    wx.redirectTo({ url: '/pages/discover/discover' });
  },
  onReady() {
    // 示例数据：可替换为后端真实数据
    const schoolData = [
      { name: '望谟县民族小学', value: 120 },
      { name: '鲁甸县第一小学', value: 80 },
      { name: '布拖县民族小学', value: 60 },
      { name: '李家堡小学', value: 100 }
    ];
    this.setData({
      barChart: {
        onInit: (canvas, width, height, dpr) => initBarChart(canvas, width, height, dpr, schoolData)
      },
      pieChart: {
        onInit: (canvas, width, height, dpr) => initPieChart(canvas, width, height, dpr, schoolData)
      }
    });
  }
}); 