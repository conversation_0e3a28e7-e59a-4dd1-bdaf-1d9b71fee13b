<view class="user-center-container">
  <view class="user-center-header">
    <view class="user-center-header-bg"></view>
    <view class="user-center-header-main">
      <view class="user-avatar-box">
        <image wx:if="{{user.avatar}}" src="{{user.avatar}}" class="user-avatar-img" mode="aspectFill" />
        <view wx:else class="user-avatar-default">头像</view>
      </view>
      <view class="user-info-box">
        <text class="user-name">{{user.name}}</text>
        <text style="color:#fff;font-size:30rpx;white-space:nowrap;">学号认证：<text style="color:{{studentAuth ? '#27ae60' : '#e74c3c'}};font-size:28rpx;display:inline;">{{studentAuth ? '已认证' : '未认证'}}</text></text>
        <!-- 评分展示 -->
        <view class="user-score-row">
          <view class="user-score-stars">
            <block wx:for="{{[1,2,3,4,5]}}" wx:key="index">
              <text class="user-score-star {{index <= starCount ? 'active' : ''}}">★</text>
            </block>
          </view>
          <text class="user-score-value">{{score}}分</text>
        </view>
      </view>
      <view class="user-center-btns-col">
        <button class="user-edit-btn-mini" bindtap="showEditModal">编辑资料</button>
        <button class="logout-btn" bindtap="onLogout">退出登录</button>
      </view>
    </view>
  </view>
  <view class="user-center-stats">
    <view class="user-stat"><text class="user-stat-num blue">12</text><text class="user-stat-label">在售</text></view>
    <view class="user-stat"><text class="user-stat-num green">8</text><text class="user-stat-label">已售</text></view>
    <view class="user-stat"><text class="user-stat-num yellow">5</text><text class="user-stat-label">收藏</text></view>
    <view class="user-stat"><text class="user-stat-num purple">3</text><text class="user-stat-label">浏览</text></view>
  </view>
  <view class="user-center-section">
    <view class="user-section-title">我的书籍</view>
    <view class="user-books-row">
      <view class="user-book-item" bindtap="onShowOnsaleBooks">
        <view class="user-book-icon-bg blue-bg"><image src="/static/images/user-book-onsale.png" class="user-book-icon" /></view>
        <text class="user-book-label">在售书籍</text>
      </view>
      <view class="user-book-item" bindtap="onShowSoldBooks">
        <view class="user-book-icon-bg green-bg"><image src="/static/images/user-book-sold.png" class="user-book-icon" /></view>
        <text class="user-book-label">已售书籍</text>
      </view>
      <view class="user-book-item" bindtap="onShowFavBooks">
        <view class="user-book-icon-bg yellow-bg"><image src="/static/images/user-book-fav.png" class="user-book-icon" /></view>
        <text class="user-book-label">收藏书籍</text>
      </view>
      <view class="user-book-item" bindtap="onShowHistoryBooks">
        <view class="user-book-icon-bg purple-bg"><image src="/static/images/user-book-history.png" class="user-book-icon" /></view>
        <text class="user-book-label">浏览记录</text>
      </view>
    </view>
  </view>
  <view class="user-center-section">
    <view class="user-section-title">其他功能</view>
    <view class="user-func-list">
      <view class="user-func-item" bindtap="goCommunity">
        <image src="/static/images/user-func-community.png" class="user-func-icon" />
        <text class="user-func-label">书友交流社区</text>
        <image src="/static/images/arrow-right.png" class="user-func-arrow" />
      </view>
      <view class="user-func-item" bindtap="goWallet">
        <image src="/static/images/user-func-wallet.png" class="user-func-icon" />
        <text class="user-func-label">我的钱包</text>
        <image src="/static/images/arrow-right.png" class="user-func-arrow" />
      </view>
      <view class="user-func-item" bindtap="goOrders">
        <image src="/static/images/user-func-order.png" class="user-func-icon" />
        <text class="user-func-label">购买记录</text>
        <image src="/static/images/arrow-right.png" class="user-func-arrow" />
      </view>
      <view class="user-func-item" bindtap="goSettings">
        <image src="/static/images/user-func-setting.png" class="user-func-icon" />
        <text class="user-func-label">设置</text>
        <image src="/static/images/arrow-right.png" class="user-func-arrow" />
      </view>
    </view>
  </view>
  <view class="user-center-tabbar-placeholder"></view>
</view>
<tabbar active="4" />

<!-- 编辑资料弹窗 -->
<view wx:if="{{showEditModal}}" class="edit-modal-mask">
  <view class="edit-modal">
    <view class="edit-modal-title">编辑资料<text class="edit-modal-close" bindtap="onCloseEditModal">×</text></view>
    <view class="edit-modal-avatar" style="display:flex;flex-direction:column;align-items:center;justify-content:center;width:100%;">
      <image src="{{editUser.avatar || '/static/images/default-avatar.png'}}" class="edit-modal-avatar-img" mode="aspectFill" />
      <button class="edit-modal-avatar-btn" bindtap="onChooseAvatar">更换头像</button>
    </view>
    <input class="edit-modal-input" placeholder="昵称" value="{{editUser.name}}" bindinput="onEditNameInput" />
    <input class="edit-modal-input" placeholder="学校" value="{{editUser.school}}" bindinput="onEditSchoolInput" />
    <picker mode="selector" range="{{genderOptions}}" value="{{editGenderIndex}}" bindchange="onEditGenderChange">
      <view class="edit-modal-input">{{genderOptions[editGenderIndex]}}</view>
    </picker>
    <button class="edit-modal-save-btn" bindtap="onSaveEdit">保存</button>
  </view>
</view>

<!-- 在售书籍列表弹窗 -->
<view wx:if="{{showOnsaleList}}" class="onsale-modal-mask">
  <view class="onsale-modal">
    <view class="onsale-modal-title">在售书籍<text class="onsale-modal-close" bindtap="onCloseOnsaleList">×</text></view>
    <block wx:for="{{onsaleBooks}}" wx:key="id">
      <view class="onsale-book-card">
        <image src="{{item.cover}}" class="onsale-book-cover" />
        <view class="onsale-book-info">
          <text class="onsale-book-title">{{item.title}}</text>
          <text class="onsale-book-price">￥{{item.price}}</text>
          <text class="onsale-book-degree">{{item.degree}}</text>
          <text class="onsale-book-type">{{item.type}}</text>
        </view>
        <view class="onsale-book-actions">
          <button class="onsale-btn edit" bindtap="onEditBook" data-id="{{item.id}}">修改</button>
          <button class="onsale-btn off" bindtap="onOffBook" data-id="{{item.id}}">下架</button>
          <button class="onsale-btn delete" bindtap="onDeleteBook" data-id="{{item.id}}">删除</button>
        </view>
      </view>
    </block>
  </view>
</view>

<!-- 编辑书籍弹窗 -->
<view wx:if="{{showEditBookModal}}" class="edit-modal-mask">
  <view class="edit-modal">
    <view class="edit-modal-title">编辑书籍<text class="edit-modal-close" bindtap="onCloseEditBookModal">×</text></view>
    <input class="edit-modal-input" placeholder="书名" value="{{editBook.title}}" data-field="title" bindinput="onEditBookInput" />
    <input class="edit-modal-input" placeholder="价格" value="{{editBook.price}}" data-field="price" bindinput="onEditBookInput" />
    <input class="edit-modal-input" placeholder="成色" value="{{editBook.degree}}" data-field="degree" bindinput="onEditBookInput" />
    <input class="edit-modal-input" placeholder="类型" value="{{editBook.type}}" data-field="type" bindinput="onEditBookInput" />
    <input class="edit-modal-input" placeholder="送达方式" value="{{editBook.delivery}}" data-field="delivery" bindinput="onEditBookInput" />
    <input class="edit-modal-input" placeholder="作者" value="{{editBook.author}}" data-field="author" bindinput="onEditBookInput" />
    <input class="edit-modal-input" placeholder="IMBA号" value="{{editBook.imba}}" data-field="imba" bindinput="onEditBookInput" />
    <textarea class="edit-modal-input" style="height:90rpx;" placeholder="书籍描述" value="{{editBook.desc}}" data-field="desc" bindinput="onEditBookInput" />
    <button class="edit-modal-save-btn" bindtap="onSaveEditBook">保存</button>
  </view>
</view>

<!-- 确认弹窗 -->
<view wx:if="{{showConfirmModal}}" class="edit-modal-mask">
  <view class="edit-modal">
    <view class="edit-modal-title">确认操作</view>
    <view style="font-size:28rpx;color:#333;margin:32rpx 0;">{{confirmType==='off'?'确定要下架这本书吗？':'确定要删除这本书吗？'}}</view>
    <view style="display:flex;gap:32rpx;width:100%;justify-content:center;">
      <button class="edit-modal-save-btn" style="background:#e5e7eb;color:#2176ff;" bindtap="onCancelModal">取消</button>
      <button class="edit-modal-save-btn" bindtap="onConfirmModal">确定</button>
    </view>
  </view>
</view>

<!-- 已售书籍列表弹窗 -->
<view wx:if="{{showSoldList}}" class="onsale-modal-mask">
  <view class="onsale-modal">
    <view class="onsale-modal-title">已售书籍<text class="onsale-modal-close" bindtap="onCloseSoldList">×</text></view>
    <block wx:for="{{soldBooks}}" wx:key="id">
      <view class="onsale-book-card">
        <image src="{{item.cover}}" class="onsale-book-cover" />
        <view class="onsale-book-info">
          <text class="onsale-book-title">{{item.title}}</text>
          <text class="onsale-book-price" style="color:#34d399;">成交价：￥{{item.price}}</text>
          <text class="onsale-book-degree">{{item.degree}}</text>
          <text class="onsale-book-type">{{item.type}}</text>
        </view>
        <view class="sold-book-status">{{item.status}}</view>
      </view>
    </block>
  </view>
</view>

<!-- 收藏书籍列表弹窗 -->
<view wx:if="{{showFavList}}" class="onsale-modal-mask">
  <view class="onsale-modal">
    <view class="onsale-modal-title">收藏书籍<text class="onsale-modal-close" bindtap="onCloseFavList">×</text></view>
    <block wx:for="{{favBooks}}" wx:key="id">
      <view class="onsale-book-card">
        <image src="{{item.cover}}" class="onsale-book-cover" />
        <view class="onsale-book-info">
          <text class="onsale-book-title">{{item.title}}</text>
          <text class="onsale-book-price">价格：￥{{item.price}}</text>
          <text class="onsale-book-degree">{{item.degree}}</text>
          <text class="onsale-book-type">{{item.type}}</text>
        </view>
        <button class="onsale-btn edit" style="width:120rpx;" bindtap="onUnfavBook" data-id="{{item.id}}">取消收藏</button>
      </view>
    </block>
  </view>
</view>

<!-- 浏览记录列表弹窗 -->
<view wx:if="{{showHistoryList}}" class="onsale-modal-mask">
  <view class="onsale-modal">
    <view class="onsale-modal-title">浏览记录<text class="onsale-modal-close" bindtap="onCloseHistoryList">×</text></view>
    <block wx:for="{{historyBooks}}" wx:key="id">
      <view class="onsale-book-card">
        <image src="{{item.cover}}" class="onsale-book-cover" />
        <view class="onsale-book-info">
          <text class="onsale-book-title">{{item.title}}</text>
          <text class="onsale-book-price">价格：￥{{item.price}}</text>
          <text class="onsale-book-degree">{{item.degree}}</text>
          <text class="onsale-book-type">{{item.type}}</text>
        </view>
        <button class="onsale-btn off" style="width:90rpx;" bindtap="onRemoveHistoryBook" data-id="{{item.id}}">移除</button>
      </view>
    </block>
  </view>
</view>

<!-- 书友交流社区弹窗 -->
<view wx:if="{{showCommunityModal}}" class="edit-modal-mask">
  <view class="edit-modal">
    <view class="edit-modal-title">书友交流社区<text class="edit-modal-close" bindtap="onCloseCommunityModal">×</text></view>
    <view style="font-size:30rpx;color:#666;text-align:center;">功能开发中，敬请期待！</view>
  </view>
</view>

<!-- 我的钱包弹窗 -->
<view wx:if="{{showWalletModal}}" class="edit-modal-mask">
  <view class="edit-modal">
    <view class="edit-modal-title">我的钱包<text class="edit-modal-close" bindtap="onCloseWalletModal">×</text></view>
    <view style="font-size:30rpx;color:#666;text-align:center;">功能开发中，敬请期待！</view>
  </view>
</view>

<!-- 购买记录弹窗 -->
<view wx:if="{{showOrderModal}}" class="edit-modal-mask">
  <view class="edit-modal">
    <view class="edit-modal-title">购买记录<text class="edit-modal-close" bindtap="onCloseOrderModal">×</text></view>
    <view style="font-size:30rpx;color:#666;text-align:center;">功能开发中，敬请期待！</view>
  </view>
</view>

<!-- 设置弹窗 -->
<view wx:if="{{showSettingModal}}" class="edit-modal-mask">
  <view class="edit-modal">
    <view class="edit-modal-title">设置<text class="edit-modal-close" bindtap="onCloseSettingModal">×</text></view>
    <view style="font-size:30rpx;color:#666;text-align:center;">功能开发中，敬请期待！</view>
  </view>
</view> 