.msg-container { background: #f5f6fa; min-height: 100vh; padding-bottom: 120rpx; }
.msg-header { display: flex; align-items: center; justify-content: center; position: relative; height: 90rpx; background: #fff; border-bottom: 1rpx solid #f0f0f0; }
.msg-title { font-size: 36rpx; font-weight: bold; color: #111; text-align: center; flex: 1; }
.msg-more-icon { position: absolute; right: 32rpx; width: 36rpx; height: 36rpx; top: 50%; transform: translateY(-50%); }
.msg-tabbar { display: flex; align-items: center; background: #fff; height: 80rpx; border-bottom: 1rpx solid #f0f0f0; position: relative; }
.msg-tab { flex: 1; text-align: center; font-size: 30rpx; color: #888; line-height: 80rpx; position: relative; }
.msg-tab.active { color: #2176ff; font-weight: bold; }
.msg-tab-underline { position: absolute; bottom: 0; left: 0; width: 25%; height: 4rpx; background: #2176ff; border-radius: 2rpx; transition: left 0.2s; }
.msg-list { padding: 0 0 40rpx 0; }
.msg-item { display: flex; align-items: flex-start; background: #fff; border-radius: 18rpx; margin: 0 24rpx 24rpx 24rpx; padding: 28rpx 24rpx; box-shadow: 0 2rpx 8rpx #e5e7eb22; position: relative; }
.msg-avatar-box { position: relative; margin-right: 22rpx; }
.msg-avatar { width: 56rpx; height: 56rpx; border-radius: 50%; background: #f3f4f6; }
.msg-unread-dot { position: absolute; top: -8rpx; right: -8rpx; background: #ff3b3b; color: #fff; font-size: 22rpx; border-radius: 50%; min-width: 32rpx; height: 32rpx; display: flex; align-items: center; justify-content: center; border: 3rpx solid #fff; font-weight: bold; }
.msg-content-box { flex: 1; min-width: 0; }
.msg-row { display: flex; align-items: center; justify-content: space-between; }
.msg-nick { font-size: 32rpx; color: #222; font-weight: bold; margin-right: 12rpx; }
.msg-time { font-size: 26rpx; color: #bbb; }
.msg-title2 { font-size: 28rpx; color: #666; margin: 6rpx 0 0 0; display: block; }
.msg-desc { font-size: 28rpx; color: #888; margin-top: 2rpx; display: block; }
.msg-item.system .msg-avatar { background: #e6f0ff; }
.msg-item.success .msg-avatar { background: #e6fae6; }
.msg-dialog-mask { position: fixed; left: 0; top: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.18); z-index: 9999; display: flex; align-items: center; justify-content: center; }
.msg-dialog { width: 96vw; max-width: 720rpx; min-width: 480rpx; background: #fff; border-radius: 32rpx; box-shadow: 0 12rpx 48rpx #2176ff33; display: flex; flex-direction: column; }
.msg-dialog-header { display: flex; align-items: center; padding: 38rpx 38rpx 0 38rpx; }
.msg-dialog-avatar { width: 68rpx; height: 68rpx; border-radius: 50%; margin-right: 22rpx; }
.msg-dialog-nick { font-size: 38rpx; color: #222; font-weight: bold; flex: 1; }
.msg-dialog-close { font-size: 54rpx; color: #bbb; margin-left: 22rpx; cursor: pointer; }
.msg-dialog-body { max-height: 480rpx; overflow-y: auto; padding: 28rpx 38rpx; display: flex; flex-direction: column; gap: 24rpx; }
.msg-dialog-row { display: flex; }
.msg-dialog-row.me { justify-content: flex-end; }
.msg-dialog-row.other { justify-content: flex-start; }
.msg-dialog-bubble { display: inline-block; padding: 24rpx 38rpx; border-radius: 28rpx; background: #e6f0ff; color: #222; font-size: 34rpx; max-width: 80vw; word-break: break-all; }
.msg-dialog-row.me .msg-dialog-bubble { background: #2176ff; color: #fff; }
.msg-dialog-inputbar { display: flex; align-items: center; border-top: 1rpx solid #f0f0f0; padding: 24rpx 38rpx; }
.msg-dialog-input { flex: 1; border-radius: 22rpx; border: 1.5rpx solid #e5e7eb; background: #f7faff; font-size: 32rpx; color: #222; padding: 0 22rpx; height: 68rpx; margin-right: 22rpx; }
.msg-dialog-sendbtn { background: #2176ff; color: #fff; border-radius: 20rpx; font-size: 32rpx; border: none; padding: 0 44rpx; height: 68rpx; }
.system-message-list {
  background: #fffbe6;
  border-radius: 18rpx;
  box-shadow: 0 2rpx 8rpx #ffe06655;
  margin: 24rpx 24rpx 0 24rpx;
  padding: 18rpx 24rpx;
}
.system-message-title {
  font-size: 28rpx;
  color: #e6b800;
  font-weight: bold;
  margin-bottom: 12rpx;
}
.system-message-item {
  margin-bottom: 10rpx;
  padding-bottom: 8rpx;
  border-bottom: 1rpx solid #ffe06655;
}
.system-message-item:last-child {
  border-bottom: none;
}
.system-message-content {
  color: #e67e22;
  font-size: 26rpx;
  font-weight: 500;
}
.system-message-time {
  color: #bbb;
  font-size: 22rpx;
  margin-left: 18rpx;
} 