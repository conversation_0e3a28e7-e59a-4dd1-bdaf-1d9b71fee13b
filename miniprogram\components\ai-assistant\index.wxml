<view class="ai-assistant">
  <!-- 消息列表 -->
  <scroll-view class="message-list" scroll-y scroll-into-view="{{scrollToMessage}}">
    <block wx:for="{{messages}}" wx:key="index">
      <view class="message {{item.role === 'user' ? 'user' : 'ai'}}" id="msg-{{index}}">
        <view class="message-content">
          <text>{{item.content}}</text>
          <view wx:if="{{item.reasoning}}" class="reasoning">
            <text>思考过程：{{item.reasoning}}</text>
          </view>
        </view>
      </view>
    </block>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-area">
    <input 
      class="input" 
      value="{{inputValue}}" 
      bindinput="onInput"
      placeholder="请输入您的问题"
      disabled="{{loading}}"
    />
    <button 
      class="send-btn" 
      bindtap="sendMessage" 
      disabled="{{loading || !inputValue.trim()}}"
    >
      {{loading ? '发送中...' : '发送'}}
    </button>
    <button class="clear-btn" bindtap="clearChat">清空</button>
  </view>
</view> 