.eco-container { background: #f6fff7; min-height: 100vh; padding-bottom: 120rpx; }
.eco-swiper { height: 320rpx; border-radius: 0 0 40rpx 40rpx; overflow: hidden; }
.eco-swiper-img { width: 100vw; height: 320rpx; object-fit: cover; }
.eco-swiper-mask { position: absolute; left: 0; top: 0; width: 100vw; height: 320rpx; display: flex; align-items: center; justify-content: center; }
.eco-swiper-slogan { color: #fff; font-size: 40rpx; font-weight: bold; text-shadow: 0 4rpx 16rpx #2ecc40cc; background: rgba(34,139,34,0.18); padding: 12rpx 36rpx; border-radius: 32rpx; }
.eco-section { margin: 32rpx 24rpx 0 24rpx; }
.eco-section-title { font-size: 34rpx; color: #2ecc40; font-weight: bold; margin-bottom: 18rpx; display: block; }
.eco-trace-scroll { display: flex; flex-direction: row; gap: 18rpx; margin-top: 12rpx; }
.eco-trace-section { margin-top: 36rpx; }
.eco-trace-card-list { background: #eaffea; border-radius: 32rpx; box-shadow: 0 2rpx 8rpx #b7e5b733; padding: 32rpx 0 18rpx 0; margin-bottom: 18rpx; display: flex; flex-direction: column; gap: 0; }
.eco-trace-card { background: none; box-shadow: none; border-radius: 0; padding: 0 38rpx 18rpx 38rpx; margin: 0; border-bottom: 1rpx solid #d6f5d6; display: flex; flex-direction: column; align-items: flex-start; }
.eco-trace-card:last-child { border-bottom: none; }
.eco-trace-school { font-size: 30rpx; color: #217a2b; font-weight: bold; margin-bottom: 2rpx; line-height: 1.2; }
.eco-trace-dest { font-size: 24rpx; color: #888; margin-bottom: 0; margin-left: 16rpx; line-height: 1.2; }
.eco-trace-map { display: flex; flex-direction: row; align-items: flex-end; gap: 0; margin: 38rpx 0 0 0; overflow-x: auto; padding-bottom: 8rpx; justify-content: center; }
.eco-trace-step { display: flex; flex-direction: column; align-items: center; min-width: 180rpx; position: relative; }
.eco-trace-dot { width: 28rpx; height: 28rpx; border-radius: 50%; background: #2ecc40; margin-bottom: 12rpx; border: 6rpx solid #eaffea; z-index: 2; box-shadow: 0 2rpx 8rpx #b7e5b733; }
.eco-trace-dot.start { background: #ffe066; border-color: #fffbe6; }
.eco-trace-dot.end { background: #217a2b; border-color: #eaffea; }
.eco-trace-line { width: 110rpx; height: 6rpx; background: linear-gradient(90deg, #2ecc40 0%, #ffe066 100%); position: absolute; top: 13rpx; left: 100%; z-index: 1; }
.eco-trace-city { font-size: 30rpx; color: #217a2b; font-weight: bold; margin-bottom: 4rpx; margin-top: 4rpx; text-align: center; letter-spacing: 2rpx; }
.eco-trace-time { font-size: 22rpx; color: #bbb; text-align: center; margin-top: 2rpx; }
.eco-thank-section { margin-top: 36rpx; }
.eco-thank-card { background: linear-gradient(120deg, #f7ffe6 0%, #fffbe6 100%); border-radius: 22rpx; box-shadow: 0 4rpx 16rpx #e5e7eb22; padding: 32rpx 28rpx; margin-bottom: 28rpx; }
.eco-thank-header { display: flex; align-items: center; gap: 18rpx; margin-bottom: 12rpx; }
.eco-thank-avatar { width: 56rpx; height: 56rpx; border-radius: 50%; background: #eaffea; }
.eco-thank-name { font-size: 30rpx; color: #217a2b; font-weight: bold; margin-right: 8rpx; }
.eco-thank-school { font-size: 26rpx; color: #bfa800; background: #fffbe6; border-radius: 12rpx; padding: 2rpx 16rpx; margin-left: 8rpx; }
.eco-thank-content { font-size: 30rpx; color: #444; margin-bottom: 18rpx; display: block; font-family: 'KaiTi', 'STKaiti', 'FangSong', 'FZKai-Z03', '楷体', serif; }
.eco-thank-footer { display: flex; align-items: center; justify-content: space-between; }
.eco-thank-time { font-size: 24rpx; color: #bbb; }
.eco-reply-btn { background: linear-gradient(90deg, #2ecc40 0%, #ffe066 100%); color: #217a2b; border: none; border-radius: 16rpx; font-size: 28rpx; padding: 0 32rpx; height: 56rpx; }
.eco-reply-mask { position: fixed; left: 0; top: 0; right: 0; bottom: 0; background: rgba(34,139,34,0.12); z-index: 9999; display: flex; align-items: center; justify-content: center; }
.eco-reply-dialog { width: 90vw; max-width: 600rpx; background: #fff; border-radius: 28rpx; box-shadow: 0 12rpx 48rpx #2ecc4033; padding: 38rpx 32rpx 32rpx 32rpx; display: flex; flex-direction: column; }
.eco-reply-title { font-size: 34rpx; color: #2ecc40; font-weight: bold; margin-bottom: 18rpx; display: flex; align-items: center; justify-content: space-between; }
.eco-reply-close { font-size: 44rpx; color: #bbb; margin-left: 18rpx; cursor: pointer; }
.eco-reply-textarea { width: 100%; min-height: 100rpx; border-radius: 16rpx; border: 1.5rpx solid #e5e7eb; background: #f7faff; font-size: 28rpx; color: #222; padding: 18rpx 24rpx; margin-bottom: 18rpx; resize: none; }
.eco-reply-send { background: linear-gradient(90deg, #2ecc40 0%, #ffe066 100%); color: #217a2b; border: none; border-radius: 16rpx; font-size: 30rpx; padding: 0 44rpx; height: 64rpx; font-weight: bold; }
.eco-points-bar { display: flex; align-items: center; background: #eaffea; border-radius: 22rpx; margin: 24rpx 24rpx 0 24rpx; padding: 18rpx 28rpx; box-shadow: 0 2rpx 8rpx #b7e5b733; gap: 18rpx; }
.eco-points-icon { width: 44rpx; height: 44rpx; margin-right: 8rpx; }
.eco-points-label { font-size: 30rpx; color: #217a2b; }
.eco-points-value { font-size: 38rpx; color: #2ecc40; font-weight: bold; margin: 0 18rpx 0 0; }
.eco-donate-btn { background: linear-gradient(90deg, #2ecc40 0%, #ffe066 100%); color: #217a2b; border: none; border-radius: 18rpx; font-size: 30rpx; padding: 0 38rpx; height: 60rpx; font-weight: bold; box-shadow: 0 2rpx 8rpx #b7e5b733; }
.eco-donate-mask { position: fixed; left: 0; top: 0; right: 0; bottom: 0; background: rgba(34,139,34,0.12); z-index: 9999; display: flex; align-items: center; justify-content: center; }
.eco-donate-dialog { width: 92vw; max-width: 650rpx; background: #fff; border-radius: 28rpx; box-shadow: 0 12rpx 48rpx #2ecc4033; padding: 38rpx 32rpx 32rpx 32rpx; display: flex; flex-direction: column; }
.eco-donate-title { font-size: 34rpx; color: #2ecc40; font-weight: bold; margin-bottom: 18rpx; display: flex; align-items: center; justify-content: space-between; }
.eco-donate-close { font-size: 44rpx; color: #bbb; margin-left: 18rpx; cursor: pointer; }
.eco-donate-upload-row { margin-bottom: 18rpx; }
.eco-donate-label { font-size: 28rpx; color: #217a2b; margin-bottom: 8rpx; display: block; }
.eco-donate-img-list { display: flex; align-items: center; gap: 14rpx; margin-top: 8rpx; }
.eco-donate-img-upload { width: 110rpx; height: 110rpx; background: #f3f4f6; border-radius: 14rpx; display: flex; align-items: center; justify-content: center; font-size: 60rpx; color: #bbb; border: 2rpx dashed #2ecc40; cursor: pointer; }
.eco-donate-img-plus { font-size: 60rpx; color: #bbb; }
.eco-donate-img-thumb { width: 110rpx; height: 110rpx; border-radius: 14rpx; object-fit: cover; }
.eco-donate-input { width: 100%; height: 64rpx; border-radius: 14rpx; border: 1.5rpx solid #e5e7eb; background: #f7faff; font-size: 28rpx; color: #222; padding: 0 18rpx; margin-bottom: 18rpx; }
.eco-donate-picker-text { color: #888; font-size: 28rpx; line-height: 64rpx; }
.eco-donate-confirm { background: linear-gradient(90deg, #2ecc40 0%, #ffe066 100%); color: #217a2b; border: none; border-radius: 16rpx; font-size: 30rpx; padding: 0 44rpx; height: 64rpx; font-weight: bold; margin-top: 12rpx; }
.eco-trace-heart { width: 36rpx; height: 36rpx; margin-bottom: 10rpx; display: block; }
.eco-chart-group {
  display: flex;
  flex-direction: row;
  gap: 32rpx;
  margin-bottom: 24rpx;
  flex-wrap: wrap;
}
.eco-chart-box {
  flex: 1 1 320rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx #e5e7eb22;
  padding: 18rpx 12rpx 12rpx 12rpx;
  margin-bottom: 0;
  min-width: 320rpx;
  max-width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.eco-chart-title {
  font-size: 28rpx;
  color: #217a2b;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-align: center;
} 