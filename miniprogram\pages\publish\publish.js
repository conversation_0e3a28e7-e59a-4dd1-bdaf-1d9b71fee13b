Page({
  data: {
    bookInfo: {
      title: '',
      author: '',
      price: '',
      type: '',
      degree: '',
      description: '',
      contact: ''
    },
    categories: [
      { name: '教材', value: '教材' },
      { name: '考研资料', value: '考研资料' },
      { name: '小说', value: '小说' },
      { name: '历史', value: '历史' },
      { name: '金融', value: '金融' }
    ],
    degrees: [
      { name: '全新', value: '全新' },
      { name: '九成新', value: '九成新' },
      { name: '八成新', value: '八成新' },
      { name: '七成新', value: '七成新' },
      { name: '六成新及以下', value: '六成新及以下' }
    ],
    tempFilePath: '',
    uploading: false
  },

  // 选择图片
  async chooseImage() {
    try {
      const res = await wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      })
      this.setData({
        tempFilePath: res.tempFilePaths[0]
      })
    } catch (error) {
      console.error('选择图片失败：', error)
    }
  },

  // 输入框内容变化
  onInput(e) {
    const { field } = e.currentTarget.dataset
    this.setData({
      [`bookInfo.${field}`]: e.detail.value
    })
  },

  // 选择分类
  onCategoryChange(e) {
    this.setData({
      'bookInfo.type': this.data.categories[e.detail.value].value
    })
  },

  // 选择新旧程度
  onDegreeChange(e) {
    this.setData({
      'bookInfo.degree': this.data.degrees[e.detail.value].value
    })
  },

  // 表单验证
  validateForm() {
    const { bookInfo, tempFilePath } = this.data
    if (!tempFilePath) {
      wx.showToast({
        title: '请上传书籍图片',
        icon: 'none'
      })
      return false
    }
    if (!bookInfo.title) {
      wx.showToast({
        title: '请输入书籍名称',
        icon: 'none'
      })
      return false
    }
    if (!bookInfo.author) {
      wx.showToast({
        title: '请输入作者',
        icon: 'none'
      })
      return false
    }
    if (!bookInfo.price) {
      wx.showToast({
        title: '请输入价格',
        icon: 'none'
      })
      return false
    }
    if (!bookInfo.type) {
      wx.showToast({
        title: '请选择分类',
        icon: 'none'
      })
      return false
    }
    if (!bookInfo.degree) {
      wx.showToast({
        title: '请选择新旧程度',
        icon: 'none'
      })
      return false
    }
    if (!bookInfo.contact) {
      wx.showToast({
        title: '请输入联系方式',
        icon: 'none'
      })
      return false
    }
    return true
  },

  // 提交发布
  async submitPublish() {
    if (!this.validateForm()) return
    if (this.data.uploading) return

    this.setData({ uploading: true })
    wx.showLoading({ title: '发布中...' })

    try {
      // 1. 上传图片到云存储
      const uploadRes = await wx.cloud.uploadFile({
        cloudPath: `book-covers/${Date.now()}.jpg`,
        filePath: this.data.tempFilePath
      })

      // 2. 调用云函数处理图片和保存书籍信息
      const result = await wx.cloud.callFunction({
        name: 'upload',
        data: {
          fileID: uploadRes.fileID,
          bookInfo: this.data.bookInfo
        }
      })

      if (result.result.code === 0) {
        wx.showToast({
          title: '发布成功',
          icon: 'success'
        })
        // 返回首页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        throw new Error(result.result.message)
      }
    } catch (error) {
      console.error('发布失败：', error)
      wx.showToast({
        title: '发布失败：' + error.message,
        icon: 'none'
      })
    } finally {
      this.setData({ uploading: false })
      wx.hideLoading()
    }
  }
}) 