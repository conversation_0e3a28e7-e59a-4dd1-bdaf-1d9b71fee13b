const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})
const db = cloud.database()

// 处理图片上传
exports.main = async (event, context) => {
  const { fileID, bookInfo } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    // 1. 下载图片
    const result = await cloud.downloadFile({
      fileID: fileID
    })
    const buffer = result.fileContent

    // 2. 上传到云存储
    const uploadResult = await cloud.uploadFile({
      cloudPath: `book-images/${openid}_${Date.now()}.jpg`,
      fileContent: buffer
    })

    // 3. 保存书籍信息到数据库
    const bookData = {
      ...bookInfo,
      coverImage: uploadResult.fileID,
      sellerId: openid,
      status: 'on_sale',
      createTime: db.serverDate(),
      updateTime: db.serverDate()
    }

    const addResult = await db.collection('books').add({
      data: bookData
    })

    return {
      code: 0,
      data: {
        bookId: addResult._id,
        fileID: uploadResult.fileID
      },
      message: '上传成功'
    }
  } catch (error) {
    console.error('上传失败：', error)
    return {
      code: -1,
      message: '上传失败：' + error.message
    }
  }
} 