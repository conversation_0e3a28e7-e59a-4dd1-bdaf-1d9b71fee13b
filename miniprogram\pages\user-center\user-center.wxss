.user-center-container {
  min-height: 100vh;
  background: #f5f6fa;
  padding-bottom: 120rpx;
}
.user-center-header {
  position: relative;
  height: 280rpx;
  margin-bottom: 32rpx;
}
.user-center-header-bg {
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  background:#4767f1e6;
  border-radius: 0 0 48rpx 48rpx;
  z-index: 1;
}
.user-center-header-main {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  height: 100%;
}
.user-avatar-box {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: transparent;
  border: 4rpx solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-right: 32rpx;
}
.user-avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}
.user-avatar-default {
  width: 100%;
  height: 100%;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: transparent;
  border: none;
}
.user-info-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  margin-top: 0;
}
.user-name {
  font-size: 50rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 2rpx;
}
.user-id {
  font-size: 32rpx;
  color: #e3e8f7;
  margin-bottom: 2rpx;
}
.user-auth {
  font-size: 125rpx;
  color: #fff;
}
.user-auth-status {
  color: #ff3b3b;
  font-weight: bold;
}
.user-center-btns-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  margin-left: 10rpx;
}
.user-edit-btn-mini,
.logout-btn {
  min-width: 0;
  width: auto;
  padding: 0 16rpx;
  font-size: 25rpx;
  height: 36rpx;
  line-height: 36rpx;
  border-radius: 20rpx;
  box-shadow: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.user-edit-btn-mini {
  background: rgba(255,255,255,0.25);
  color: #fff;
  border: none;
}
.logout-btn {
  background: #fff;
  color: #2176ff;
  border: none;
}
.user-center-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #fff;
  border-radius: 20rpx;
  margin: 0 24rpx 32rpx 24rpx;
  padding: 24rpx 0;
  box-shadow: 0 2rpx 8rpx #e5e7eb22;
}
.user-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.user-stat-num {
  font-size: 45rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}
.user-stat-num.blue { color: #2176ff; }
.user-stat-num.green { color: #34d399; }
.user-stat-num.yellow { color: #ffd600; }
.user-stat-num.purple { color: #a78bfa; }
.user-stat-label {
  font-size: 22rpx;
  color: #888;
}
.user-center-section {
  background: #fff;
  border-radius: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  padding: 30rpx 0 0 0;
  box-shadow: 0 2rpx 8rpx #e5e7eb22;
}
.user-section-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #222;
  margin-left: 32rpx;
  margin-bottom: 18rpx;
}
.user-books-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 25rpx;
}
.user-book-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.user-book-icon-bg {
  width: 68rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
}
.user-book-icon {
  width: 40rpx;
  height: 36rpx;
}
.blue-bg { background: #e6f0ff; }
.green-bg { background: #e6fae6; }
.yellow-bg { background: #fffbe6; }
.purple-bg { background: #f5eaff; }
.user-book-label {
  font-size: 22rpx;
  color: #222;
  font-weight: 600;
}
.user-func-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 12rpx;
}
.user-func-item {
  display: flex;
  align-items: center;
  font-size: 25rpx;
  color: #333;
  padding: 18rpx 32rpx;
  border-bottom: 1rpx solid #f3f4f6;
  position: relative;
}
.user-func-icon {
  width: 38rpx;
  height: 35rpx;
  margin-right: 18rpx;
}
.user-func-label {
  font-size: 30rpx;
  color: #222;
  font-weight: 600;
  margin-left: 8rpx;
}
.user-func-arrow {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 24rpx;
  height: 24rpx;
  display: inline-block;
}
.user-center-tabbar-placeholder {
  height: 120rpx;
}
.tabbar {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  box-shadow: 0 -2rpx 8rpx #e5e7eb;
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100rpx;
  z-index: 99;
}
.tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 22rpx;
  color: #888;
  flex: 1;
  justify-content: center;
}
.tabbar-icon {
  width: 40rpx;
  height: 40rpx;
  display: block;
  margin-bottom: 4rpx;
}
.tabbar-item.tabbar-item-add {
  position: relative;
  z-index: 10;
  margin-top: -28rpx;
}
.tabbar-icon-add {
  width: 72rpx;
  height: 72rpx;
  background: #2176ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx #b3c6ff44;
  margin-bottom: 0;
  margin-top: 0;
}
.tabbar-icon-add::after {
  content: '';
  display: block;
}
.tabbar-item.tabbar-item-add .tabbar-text {
  color: #2176ff !important;
  margin-top: 2rpx;
}
.tabbar-text {
  color: #888;
  transition: color 0.2s;
  font-size: 22rpx;
}
.tabbar-text.active {
  color: #2176ff !important;
  font-weight: bold;
}
.tabbar-item .tabbar-icon {
  filter: grayscale(1) brightness(1.2);
  transition: filter 0.2s;
}
.tabbar-item .tabbar-text.active,
.tabbar-item.active .tabbar-icon {
  color: #2176ff !important;
  filter: none;
}
.edit-modal-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}
.edit-modal {
  width: 90vw;
  max-width: 650rpx;
  background: linear-gradient(135deg, #f7faff 0%, #e6f0ff 100%);
  border-radius: 36rpx;
  box-shadow: 0 12rpx 48rpx #2176ff33;
  padding: 56rpx 36rpx 40rpx 36rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  animation: popIn 0.25s cubic-bezier(.68,-0.55,.27,1.55);
}
.edit-modal-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #2176ff;
  margin-bottom: 36rpx;
  width: 100%;
  text-align: left;
  letter-spacing: 1rpx;
}
.edit-modal-close {
  position: absolute;
  right: 36rpx;
  top: 56rpx;
  font-size: 44rpx;
  color: #bbb;
  font-weight: normal;
  cursor: pointer;
  transition: color 0.2s;
}
.edit-modal-close:hover {
  color: #2176ff;
}
.edit-modal-avatar-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #2176ff;
  object-fit: cover;
  margin-bottom: 12rpx;
  background: linear-gradient(135deg, #e6f0ff 0%, #f7faff 100%);
  box-shadow: 0 4rpx 16rpx #2176ff22;
}
.edit-modal-avatar-btn {
  background: #e6f0ff;
  color: #2176ff;
  border-radius: 20rpx;
  font-size: 24rpx;
  padding: 8rpx 32rpx;
  border: none;
  margin-bottom: 18rpx;
  font-weight: 500;
  letter-spacing: 1rpx;
  box-shadow: 0 2rpx 8rpx #b3c6ff22;
}
.edit-modal-input {
  width: 100%;
  height: 64rpx;
  border-radius: 18rpx;
  border: 1.5rpx solid #e5e7eb;
  background: #f7faff;
  font-size: 30rpx;
  color: #222;
  margin-bottom: 28rpx;
  padding: 0 24rpx;
  transition: border 0.2s;
  box-shadow: 0 2rpx 8rpx #e5e7eb22;
}
.edit-modal-input:focus {
  border: 1.5rpx solid #2176ff;
}
.edit-modal-save-btn {
  width: 100%;
  height: 68rpx;
  background: linear-gradient(90deg, #2176ff 0%, #5faaff 100%);
  color: #fff;
  border-radius: 22rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-top: 18rpx;
  box-shadow: 0 4rpx 16rpx #2176ff22;
  letter-spacing: 2rpx;
  transition: background 0.2s;
}
.onsale-book-card {
  display: flex;
  align-items: flex-start;
  background: #f7faff;
  border-radius: 28rpx;
  box-shadow: 0 2rpx 12rpx #e5e7eb33;
  padding: 32rpx 18rpx 24rpx 18rpx;
  margin-bottom: 32rpx;
  width: 100%;
  position: relative;
}
.onsale-book-cover {
  width: 90rpx;
  height: 110rpx;
  border-radius: 14rpx;
  background: #e5e7eb;
  object-fit: cover;
  margin-right: 18rpx;
  box-shadow: 0 2rpx 8rpx #b3c6ff22;
}
.onsale-book-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.onsale-book-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 2rpx;
  letter-spacing: 1rpx;
}
.onsale-book-price {
  font-size: 28rpx;
  color: #ff3b3b;
  font-weight: bold;
}
.onsale-book-degree {
  font-size: 24rpx;
  color: #888;
}
.onsale-book-type {
  font-size: 24rpx;
  color: #2176ff;
}
.onsale-book-actions,
.onsale-book-card > .onsale-btn {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  position: absolute;
  bottom: 18rpx;
  right: 18rpx;
  align-items: flex-end;
}
.onsale-btn {
  min-width: 48rpx;
  height: 28rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 2rpx 8rpx #e5e7eb22;
  margin: 0;
  padding: 0 12rpx;
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}
.onsale-btn.edit {
  background: #e6f0ff;
  color: #2176ff;
}
.onsale-btn.off {
  background: #fffbe6;
  color: #ffb300;
}
.onsale-btn.delete {
  background: #ffeaea;
  color: #ff3b3b;
}
.sold-book-status {
  font-size: 26rpx;
  color: #34d399;
  font-weight: bold;
  margin-left: 18rpx;
  min-width: 80rpx;
  text-align: right;
}
.onsale-modal-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}
.onsale-modal {
  width: 90vw;
  max-width: 650rpx;
  background: #fff;
  border-radius: 36rpx;
  box-shadow: 0 12rpx 48rpx #2176ff33;
  padding: 56rpx 36rpx 40rpx 36rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  animation: popIn 0.25s cubic-bezier(.68,-0.55,.27,1.55);
}
.user-score-row {
  display: flex;
  align-items: center;
  margin-top: 12rpx;
  margin-bottom: 8rpx;
  justify-content: flex-start;
}
.user-score-stars {
  display: flex;
  align-items: center;
  margin-right: 12rpx;
}
.user-score-star {
  font-size: 32rpx;
  color: #e0e0e0;
  margin-right: 2rpx;
}
.user-score-star.active {
  color: #ffc107;
  text-shadow: 0 2rpx 8rpx #ffe06699;
}
.user-score-value {
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
} 