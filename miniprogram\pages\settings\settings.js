Page({
  data: {
    showPwdModal: false,
    showPhoneModal: false,
    showStudentModal: false,
    oldPwd: '', newPwd: '', confirmPwd: '',
    phone: '', studentId: ''
  },
  onChangePwd() { this.setData({ showPwdModal: true }); },
  onClosePwdModal() { this.setData({ showPwdModal: false, oldPwd: '', newPwd: '', confirmPwd: '' }); },
  onOldPwdInput(e) { this.setData({ oldPwd: e.detail.value }); },
  onNewPwdInput(e) { this.setData({ newPwd: e.detail.value }); },
  onConfirmPwdInput(e) { this.setData({ confirmPwd: e.detail.value }); },
  onSavePwd() {
    if (!this.data.oldPwd || !this.data.newPwd || !this.data.confirmPwd) return wx.showToast({ title: '请填写完整', icon: 'none' });
    if (this.data.newPwd !== this.data.confirmPwd) return wx.showToast({ title: '两次密码不一致', icon: 'none' });
    this.setData({ showPwdModal: false, oldPwd: '', newPwd: '', confirmPwd: '' });
    wx.showToast({ title: '修改成功', icon: 'success' });
  },
  onBindPhone() { this.setData({ showPhoneModal: true }); },
  onClosePhoneModal() { this.setData({ showPhoneModal: false, phone: '' }); },
  onPhoneInput(e) { this.setData({ phone: e.detail.value }); },
  onSavePhone() {
    if (!this.data.phone) return wx.showToast({ title: '请输入手机号', icon: 'none' });
    this.setData({ showPhoneModal: false, phone: '' });
    wx.showToast({ title: '绑定成功', icon: 'success' });
  },
  onBindStudent() { this.setData({ showStudentModal: true }); },
  onCloseStudentModal() { this.setData({ showStudentModal: false, studentId: '' }); },
  onStudentInput(e) { this.setData({ studentId: e.detail.value }); },
  onSaveStudent() {
    if (!this.data.studentId) return wx.showToast({ title: '请输入学号', icon: 'none' });
    this.setData({ showStudentModal: false, studentId: this.data.studentId });
    try {
      wx.setStorageSync('studentAuth', true);
    } catch (e) {}
    wx.showToast({ title: '绑定成功', icon: 'success' });
  },
  onUnbindStudent() {
    this.setData({ studentId: '', showStudentModal: false });
    try {
      wx.setStorageSync('studentAuth', false);
    } catch (e) {}
    wx.showToast({ title: '已解除绑定', icon: 'success' });
    // 可选：通知个人中心页面刷新
  },
  onRebindStudent() {
    this.setData({ studentId: '' });
  }
}) 