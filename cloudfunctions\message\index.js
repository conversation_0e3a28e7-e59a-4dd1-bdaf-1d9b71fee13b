const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})
const db = cloud.database()
const _ = db.command

// 消息通知云函数
exports.main = async (event, context) => {
  const { type, data } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  switch (type) {
    // 获取消息列表
    case 'getMessages':
      try {
        const { page = 1, pageSize = 20, messageType, isRead } = data
        let query = { userId: openid }
        
        if (messageType) {
          query.type = messageType
        }
        
        if (typeof isRead === 'boolean') {
          query.isRead = isRead
        }

        const result = await db.collection('messages')
          .where(query)
          .orderBy('createTime', 'desc')
          .skip((page - 1) * pageSize)
          .limit(pageSize)
          .get()

        // 获取未读消息数量
        const unreadCount = await db.collection('messages')
          .where({
            userId: openid,
            isRead: false
          })
          .count()

        return {
          code: 0,
          data: {
            list: result.data,
            total: result.data.length,
            unreadCount: unreadCount.total,
            page,
            pageSize
          },
          message: '获取成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '获取消息失败',
          error: error.message
        }
      }

    // 发送消息
    case 'sendMessage':
      try {
        const { userId, messageType, title, content, relatedId } = data
        
        if (!userId || !title || !content) {
          return {
            code: 400,
            message: '参数不完整'
          }
        }

        const messageData = {
          userId,
          type: messageType || 'system',
          title,
          content,
          relatedId: relatedId || '',
          isRead: false,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }

        const result = await db.collection('messages').add({
          data: messageData
        })

        return {
          code: 0,
          data: { messageId: result._id },
          message: '发送成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '发送消息失败',
          error: error.message
        }
      }

    // 标记消息为已读
    case 'markAsRead':
      try {
        const { messageId, markAll } = data
        
        if (markAll) {
          // 标记所有未读消息为已读
          await db.collection('messages')
            .where({
              userId: openid,
              isRead: false
            })
            .update({
              data: {
                isRead: true,
                updateTime: db.serverDate()
              }
            })
        } else if (messageId) {
          // 标记单条消息为已读
          const message = await db.collection('messages').doc(messageId).get()
          if (!message.data) {
            return {
              code: 404,
              message: '消息不存在'
            }
          }
          
          if (message.data.userId !== openid) {
            return {
              code: 403,
              message: '无权限操作'
            }
          }

          await db.collection('messages').doc(messageId).update({
            data: {
              isRead: true,
              updateTime: db.serverDate()
            }
          })
        } else {
          return {
            code: 400,
            message: '参数错误'
          }
        }

        return {
          code: 0,
          message: '操作成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '标记失败',
          error: error.message
        }
      }

    // 删除消息
    case 'deleteMessage':
      try {
        const { messageId } = data
        
        if (!messageId) {
          return {
            code: 400,
            message: '消息ID不能为空'
          }
        }

        const message = await db.collection('messages').doc(messageId).get()
        if (!message.data) {
          return {
            code: 404,
            message: '消息不存在'
          }
        }
        
        if (message.data.userId !== openid) {
          return {
            code: 403,
            message: '无权限删除'
          }
        }

        await db.collection('messages').doc(messageId).remove()

        return {
          code: 0,
          message: '删除成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '删除失败',
          error: error.message
        }
      }

    // 发送系统通知（管理员功能）
    case 'sendSystemNotice':
      try {
        const { title, content, targetUsers } = data
        
        if (!title || !content) {
          return {
            code: 400,
            message: '标题和内容不能为空'
          }
        }

        // 如果指定了目标用户，只发送给指定用户；否则发送给所有用户
        if (targetUsers && Array.isArray(targetUsers)) {
          for (const userId of targetUsers) {
            await db.collection('messages').add({
              data: {
                userId,
                type: 'system',
                title,
                content,
                relatedId: '',
                isRead: false,
                createTime: db.serverDate(),
                updateTime: db.serverDate()
              }
            })
          }
        } else {
          // 获取所有用户
          const users = await db.collection('users').get()
          for (const user of users.data) {
            await db.collection('messages').add({
              data: {
                userId: user._id,
                type: 'system',
                title,
                content,
                relatedId: '',
                isRead: false,
                createTime: db.serverDate(),
                updateTime: db.serverDate()
              }
            })
          }
        }

        return {
          code: 0,
          message: '系统通知发送成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '发送系统通知失败',
          error: error.message
        }
      }

    // 发送交易相关消息
    case 'sendTradeMessage':
      try {
        const { buyerId, sellerId, orderId, messageType, customContent } = data
        
        let title, content
        
        switch (messageType) {
          case 'order_created':
            title = '新订单通知'
            content = customContent || '您有一个新的订单，请及时处理'
            break
          case 'order_paid':
            title = '订单支付通知'
            content = customContent || '买家已支付，请及时发货'
            break
          case 'order_shipped':
            title = '订单发货通知'
            content = customContent || '卖家已发货，请注意查收'
            break
          case 'order_completed':
            title = '交易完成通知'
            content = customContent || '交易已完成，感谢您的使用'
            break
          default:
            title = '交易通知'
            content = customContent || '您有新的交易消息'
        }

        // 发送给买家
        if (buyerId) {
          await db.collection('messages').add({
            data: {
              userId: buyerId,
              type: 'trade',
              title,
              content,
              relatedId: orderId || '',
              isRead: false,
              createTime: db.serverDate(),
              updateTime: db.serverDate()
            }
          })
        }

        // 发送给卖家
        if (sellerId && sellerId !== buyerId) {
          await db.collection('messages').add({
            data: {
              userId: sellerId,
              type: 'trade',
              title,
              content,
              relatedId: orderId || '',
              isRead: false,
              createTime: db.serverDate(),
              updateTime: db.serverDate()
            }
          })
        }

        return {
          code: 0,
          message: '交易消息发送成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '发送交易消息失败',
          error: error.message
        }
      }

    default:
      return {
        code: 404,
        message: '不支持的操作类型'
      }
  }
}
