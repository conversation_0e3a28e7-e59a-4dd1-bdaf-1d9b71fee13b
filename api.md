# 二手书小程序接口文档

> 所有接口均遵循 @api-request-rule.mdc 规范，参数采用驼峰命名，返回结构统一，所有接口返回均包含 `code`、`msg` 字段。

---

## 1. 用户注册

**接口名称**  
用户注册

**接口核心功能描述**  
新用户注册账号，需填写昵称、密码、手机号、邮箱、性别。

**接口地址**  
`/api/user/register`

**方法**  
POST

**需要登录**  
否

**请求参数**
```json
{
  "nickname": "string",
  "password": "string",
  "phone": "string",
  "email": "string",
  "gender": "string" // 男/女/保密
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "注册成功",
  "data": {
    "userId": "string"
  }
}
```

---

## 2. 用户登录

**接口名称**  
用户登录

**接口核心功能描述**  
支持手机号+密码、微信授权、昵称+密码三种方式登录。

**接口地址**  
`/api/user/login`

**方法**  
POST

**需要登录**  
否

**请求参数**
```json
{
  "loginType": "phone|wechat|nickname",
  "phone": "string",      // loginType=phone时必填
  "password": "string",   // loginType=phone/nickname时必填
  "nickname": "string",   // loginType=nickname时必填
  "wechatCode": "string"  // loginType=wechat时必填
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "登录成功",
  "data": {
    "token": "string",
    "userInfo": {
      "userId": "string",
      "nickname": "string",
      "avatar": "string",
      "phone": "string",
      "email": "string",
      "gender": "string"
    }
  }
}
```

---

## 3. 获取首页书籍列表

**接口名称**  
首页书籍列表

**接口核心功能描述**  
获取首页展示的二手书籍列表，支持分页和筛选。

**接口地址**  
`/api/book/list`

**方法**  
GET

**需要登录**  
否

**请求参数**
```json
{
  "page": 1,
  "pageSize": 20,
  "keyword": "string", // 可选，搜索关键字
  "category": "string" // 可选，书籍分类
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "list": [
      {
        "bookId": "string",
        "title": "string",
        "cover": "string",
        "price": 20,
        "degree": "string",
        "type": "string",
        "sellerId": "string"
      }
    ],
    "total": 100
  }
}
```

---

## 4. 发布二手书

**接口名称**  
发布二手书

**接口核心功能描述**  
用户发布二手书信息，需填写书名、价格、成色、类型、图片等。

**接口地址**  
`/api/book/publish`

**方法**  
POST

**需要登录**  
是

**请求参数**
```json
{
  "title": "string",
  "cover": "string",
  "price": 20,
  "degree": "string",
  "type": "string",
  "desc": "string"
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "发布成功",
  "data": {
    "bookId": "string"
  }
}
```

---

## 5. 获取书籍详情

**接口名称**  
书籍详情

**接口核心功能描述**  
获取指定书籍的详细信息。

**接口地址**  
`/api/book/detail/{bookId}`

**方法**  
GET

**需要登录**  
否

**请求参数**  
无（bookId在url中）

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "bookId": "string",
    "title": "string",
    "cover": "string",
    "price": 20,
    "degree": "string",
    "type": "string",
    "desc": "string",
    "sellerId": "string",
    "sellerInfo": {
      "userId": "string",
      "nickname": "string",
      "avatar": "string"
    }
  }
}
```

---

## 6. 下单购买

**接口名称**  
下单购买

**接口核心功能描述**  
用户对某本书发起购买订单。

**接口地址**  
`/api/order/create`

**方法**  
POST

**需要登录**  
是

**请求参数**
```json
{
  "bookId": "string",
  "buyerId": "string",
  "address": "string"
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "下单成功",
  "data": {
    "orderId": "string"
  }
}
```

---

## 7. 获取我的订单

**接口名称**  
我的订单

**接口核心功能描述**  
获取当前用户的订单列表。

**接口地址**  
`/api/order/list`

**方法**  
GET

**需要登录**  
是

**请求参数**
```json
{
  "userId": "string",
  "page": 1,
  "pageSize": 20
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "list": [
      {
        "orderId": "string",
        "bookId": "string",
        "status": "string",
        "createTime": "string"
      }
    ],
    "total": 10
  }
}
```

---

## 8. 个人信息获取与修改

**接口名称**  
获取/修改个人信息

**接口核心功能描述**  
获取当前用户信息或修改用户资料。

**接口地址**  
`/api/user/info`（GET获取，POST修改）

**方法**  
GET/POST

**需要登录**  
是

**请求参数（POST）**
```json
{
  "nickname": "string",
  "avatar": "string",
  "email": "string",
  "gender": "string",
  "school": "string"
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "userId": "string",
    "nickname": "string",
    "avatar": "string",
    "email": "string",
    "gender": "string",
    "school": "string"
  }
}
```

---

## 9. 退出登录

**接口名称**  
退出登录

**接口核心功能描述**  
用户主动退出登录，服务端清理token。

**接口地址**  
`/api/user/logout`

**方法**  
POST

**需要登录**  
是

**请求参数**  
无

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "退出成功"
}
```

---

## 10. 消息通知

**接口名称**  
消息通知

**接口核心功能描述**  
获取用户的系统消息、交易消息等。

**接口地址**  
`/api/message/list`

**方法**  
GET

**需要登录**  
是

**请求参数**
```json
{
  "userId": "string",
  "page": 1,
  "pageSize": 20
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "list": [
      {
        "messageId": "string",
        "type": "system|trade|other",
        "content": "string",
        "time": "string",
        "unread": true
      }
    ],
    "total": 5
  }
}
```

---

## 11. 钱包余额查询

**接口名称**  
钱包余额查询

**接口核心功能描述**  
获取当前用户的钱包余额和流水。

**接口地址**  
`/api/wallet/info`

**方法**  
GET

**需要登录**  
是

**请求参数**
```json
{
  "userId": "string"
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "balance": 100.5,
    "transactions": [
      {
        "id": "string",
        "type": "income|expense",
        "amount": 10.5,
        "desc": "string",
        "time": "string"
      }
    ]
  }
}
```

---

## 12. 钱包提现

**接口名称**  
钱包提现

**接口核心功能描述**  
用户将钱包余额提现到银行卡或微信。

**接口地址**  
`/api/wallet/withdraw`

**方法**  
POST

**需要登录**  
是

**请求参数**
```json
{
  "userId": "string",
  "amount": 50.0,
  "accountType": "bank|wechat",
  "accountInfo": "string"
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "提现申请成功"
}
```

---

## 13. 社区帖子列表

**接口名称**  
社区帖子列表

**接口核心功能描述**  
获取社区的帖子列表，支持分页。

**接口地址**  
`/api/community/posts`

**方法**  
GET

**需要登录**  
否

**请求参数**
```json
{
  "page": 1,
  "pageSize": 20
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "list": [
      {
        "postId": "string",
        "title": "string",
        "content": "string",
        "authorId": "string",
        "authorName": "string",
        "createTime": "string"
      }
    ],
    "total": 100
  }
}
```

---

## 14. 社区发帖

**接口名称**  
社区发帖

**接口核心功能描述**  
用户在社区发布新帖子。

**接口地址**  
`/api/community/post`

**方法**  
POST

**需要登录**  
是

**请求参数**
```json
{
  "title": "string",
  "content": "string",
  "authorId": "string"
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "发帖成功",
  "data": {
    "postId": "string"
  }
}
```

---

## 15. 设置-获取用户设置

**接口名称**  
获取用户设置

**接口核心功能描述**  
获取当前用户的偏好设置。

**接口地址**  
`/api/settings/get`

**方法**  
GET

**需要登录**  
是

**请求参数**
```json
{
  "userId": "string"
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "notifyOnMsg": true,
    "theme": "light|dark"
  }
}
```

---

## 16. 设置-修改用户设置

**接口名称**  
修改用户设置

**接口核心功能描述**  
修改当前用户的偏好设置。

**接口地址**  
`/api/settings/update`

**方法**  
POST

**需要登录**  
是

**请求参数**
```json
{
  "userId": "string",
  "notifyOnMsg": true,
  "theme": "light|dark"
}
```

**响应类型**  
application/json

**返回值**
```json
{
  "code": 0,
  "msg": "设置已更新"
}
```

--- 