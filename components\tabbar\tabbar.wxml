<view class="tabbar">
  <view class="tabbar-item" bindtap="onTabbarTap" data-index="0">
    <image src="/static/images/tab-home.png" class="tabbar-icon {{active === 0 ? 'active' : 'inactive'}}" />
    <text class="tabbar-text {{active === 0 ? 'active' : 'inactive'}}">首页</text>
  </view>
  <view class="tabbar-item" bindtap="onTabbarDiscover" data-index="1">
    <image src="/static/images/tab-discover.png" class="tabbar-icon {{active === 1 ? 'active' : 'inactive'}}" />
    <text class="tabbar-text {{active === 1 ? 'active' : 'inactive'}}">发现</text>
  </view>
  <view class="tabbar-item tabbar-item-add" bindtap="onTabbarPublish">
    <view class="tabbar-icon-add tabbar-icon-publish always-blue">
      <text class="tabbar-plus">+</text>
    </view>
    <text class="tabbar-text tabbar-text-publish always-blue">发布</text>
  </view>
  <view class="tabbar-item" bindtap="onTabbarMessage" data-index="3">
    <image src="/static/images/tab-message.png" class="tabbar-icon {{active === 3 ? 'active' : 'inactive'}}" />
    <text class="tabbar-text {{active === 3 ? 'active' : 'inactive'}}">消息</text>
  </view>
  <view class="tabbar-item" bindtap="onTabbarTap" data-index="4">
    <image src="/static/images/tab-user.png" class="tabbar-icon {{active === 4 ? 'active' : 'inactive'}}" />
    <text class="tabbar-text {{active === 4 ? 'active' : 'inactive'}}">我的</text>
  </view>
</view> 