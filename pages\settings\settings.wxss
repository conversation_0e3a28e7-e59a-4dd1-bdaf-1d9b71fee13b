.settings-container { min-height: 100vh; background: #f5f6fa; padding: 48rpx; }
.settings-header { font-size: 52rpx; font-weight: bold; color: #2176ff; margin-bottom: 40rpx; }
.settings-list { display: flex; flex-direction: column; gap: 40rpx; }
.settings-item { background: #fff; border-radius: 24rpx; box-shadow: 0 4rpx 16rpx #e5e7eb22; font-size: 38rpx; color: #2176ff; font-weight: bold; padding: 40rpx 40rpx 40rpx 100rpx; text-align: left; border: none; position: relative; }
.settings-icon { width: 48rpx; height: 48rpx; position: absolute; left: 32rpx; top: 50%; transform: translateY(-50%); }
.settings-modal { border-radius: 36rpx; box-shadow: 0 12rpx 48rpx #2176ff33; padding: 56rpx 36rpx 40rpx 36rpx; background: #fff; }
.settings-modal-title { font-size: 44rpx; color: #2176ff; font-weight: bold; margin-bottom: 36rpx; }
.input-row { display: flex; align-items: center; margin-bottom: 32rpx; }
.input-icon { width: 40rpx; height: 40rpx; margin-right: 18rpx; }
.modal-input { flex: 1; border-radius: 18rpx; border: 1.5rpx solid #e5e7eb; background: #f7faff; font-size: 34rpx; color: #222; padding: 0 24rpx; height: 64rpx; }
.settings-modal-btn { width: 100%; height: 80rpx; background: linear-gradient(90deg, #2176ff 0%, #5faaff 100%); color: #fff; border-radius: 22rpx; font-size: 38rpx; font-weight: bold; border: none; margin-top: 18rpx; }
.modal-title { font-size: 44rpx; }
.modal-input, .modal-btn { font-size: 36rpx; height: 72rpx; border-radius: 20rpx; }
.modal-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
} 