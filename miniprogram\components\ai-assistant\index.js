Component({
  data: {
    messages: [],
    inputValue: '',
    loading: false
  },

  methods: {
    // 发送消息
    async sendMessage() {
      if (!this.data.inputValue.trim() || this.data.loading) return

      const userMessage = {
        role: 'user',
        content: this.data.inputValue
      }

      // 更新消息列表
      this.setData({
        messages: [...this.data.messages, userMessage],
        inputValue: '',
        loading: true
      })

      try {
        // 调用云函数
        const { result } = await wx.cloud.callFunction({
          name: 'ai-assistant',
          data: {
            name: 'chat',
            data: {
              messages: this.data.messages
            }
          }
        })

        if (result.code === 0) {
          // 添加AI回复
          const aiMessage = {
            role: 'assistant',
            content: result.answer,
            reasoning: result.reasoning
          }
          this.setData({
            messages: [...this.data.messages, aiMessage]
          })
        } else {
          wx.showToast({
            title: result.msg || '请求失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('AI请求失败:', error)
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
      } finally {
        this.setData({ loading: false })
      }
    },

    // 输入框内容变化
    onInput(e) {
      this.setData({
        inputValue: e.detail.value
      })
    },

    // 清空对话
    clearChat() {
      this.setData({
        messages: []
      })
    }
  }
}) 