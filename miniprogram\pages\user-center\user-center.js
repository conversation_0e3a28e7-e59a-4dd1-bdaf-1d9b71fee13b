Page({
  data: {
    user: {
      avatar: '',
      name: '张三',
      id: '12345678',
      auth: false,
      school: '',
      gender: ''
    },
    activeTabbarIndex: 4,
    showEditModal: false,
    editUser: {},
    genderOptions: ['男', '女'],
    editGenderIndex: 0,
    // 在售书籍区块数据
    onsaleBooks: [
      { id: 1, cover: '/static/images/book1.png', title: '高等数学（第七版）', price: 25, degree: '9成新', type: '教材' },
      { id: 2, cover: '/static/images/book2.png', title: '数据结构与算法分析', price: 35, degree: '8成新', type: '教材' }
    ],
    showOnsaleList: false,
    showEditBookModal: false,
    showConfirmModal: false,
    editBook: {},
    confirmType: '', // 'off' or 'delete'
    confirmBookId: null,
    soldBooks: [
      { id: 101, cover: '/static/images/book3.png', title: '线性代数（第六版）', price: 20, degree: '8成新', type: '教材', status: '已售出' }
    ],
    showSoldList: false,
    favBooks: [
      { id: 201, cover: '/static/images/book4.png', title: '概率论与数理统计', price: 22, degree: '9成新', type: '教材' }
    ],
    showFavList: false,
    historyBooks: [
      { id: 301, cover: '/static/images/book5.png', title: '操作系统原理', price: 28, degree: '9成新', type: '教材' }
    ],
    showHistoryList: false,
    showCommunityModal: false,
    showWalletModal: false,
    showOrderModal: false,
    showSettingModal: false,
    studentAuth: false,
    score: 100,
    starCount: 5,
    maliciousComments: 0,
    fakeTrades: 0,
    messages: [],
  },
  onTabbarTap(e) {
    const index = Number(e.currentTarget.dataset.index);
    if (index === this.data.activeTabbarIndex) return;
    this.setData({ activeTabbarIndex: index });
    if (index === 0) {
      wx.redirectTo({ url: '/pages/index/index' });
    } else if (index === 4) {
      wx.redirectTo({ url: '/pages/user-center/user-center' });
    } else {
      wx.showToast({ title: '功能开发中', icon: 'none' });
    }
  },
  onTabbarPublish() {
    wx.navigateTo({ url: '/pages/publish/publish' });
  },
  onTabbarMessage() {
    this.setData({ activeTabbarIndex: 3 });
    wx.redirectTo({ url: '/pages/message/message' });
  },
  onTabbarDiscover() {
    this.setData({ activeTabbarIndex: 1 });
    wx.redirectTo({ url: '/pages/discover/discover' });
  },
  // 显示在售书籍列表
  onShowOnsaleBooks() {
    this.setData({ showOnsaleList: true });
  },
  // 关闭在售书籍列表
  onCloseOnsaleList() {
    this.setData({ showOnsaleList: false });
  },
  // 编辑书籍
  onEditBook(e) {
    const id = e.currentTarget.dataset.id;
    const book = this.data.onsaleBooks.find(b => b.id === id);
    this.setData({ showEditBookModal: true, editBook: { ...book } });
  },
  // 编辑弹窗输入
  onEditBookInput(e) {
    const field = e.currentTarget.dataset.field;
    let value = e.detail.value;
    // 价格转数字
    if (field === 'price') value = Number(value);
    this.setData({ [`editBook.${field}`]: value });
  },
  // 保存编辑
  onSaveEditBook() {
    // 修改本地缓存
    let books = this.data.onsaleBooks.map(b => b.id === this.data.editBook.id ? { ...this.data.editBook } : b);
    wx.setStorageSync('publishedBooks', books);
    this.setData({ onsaleBooks: books, showEditBookModal: false });
    wx.showToast({ title: '保存成功', icon: 'success' });
  },
  // 关闭编辑弹窗
  onCloseEditBookModal() {
    this.setData({ showEditBookModal: false });
  },
  // 下架
  onOffBook(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({ showConfirmModal: true, confirmType: 'off', confirmBookId: id });
  },
  // 删除
  onDeleteBook(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({ showConfirmModal: true, confirmType: 'delete', confirmBookId: id });
  },
  // 确认弹窗-确定
  onConfirmModal() {
    const { confirmType, confirmBookId, onsaleBooks } = this.data;
    let books = onsaleBooks;
    if (confirmType === 'off' || confirmType === 'delete') {
      books = onsaleBooks.filter(b => b.id !== confirmBookId);
      wx.setStorageSync('publishedBooks', books);
      this.setData({ onsaleBooks: books, showConfirmModal: false });
      wx.showToast({ title: confirmType === 'off' ? '已下架' : '已删除', icon: 'success' });
    }
  },
  // 确认弹窗-取消
  onCancelModal() {
    this.setData({ showConfirmModal: false });
  },
  // 显示编辑资料弹窗
  showEditModal() {
    const { user, genderOptions } = this.data;
    this.setData({
      showEditModal: true,
      editUser: { ...user },
      editGenderIndex: genderOptions.indexOf(user.gender) >= 0 ? genderOptions.indexOf(user.gender) : 0
    });
  },
  // 关闭弹窗
  onCloseEditModal() {
    this.setData({ showEditModal: false });
  },
  // 输入昵称
  onEditNameInput(e) {
    this.setData({ 'editUser.name': e.detail.value });
  },
  // 输入学校
  onEditSchoolInput(e) {
    this.setData({ 'editUser.school': e.detail.value });
  },
  // 选择性别
  onEditGenderChange(e) {
    const idx = e.detail.value;
    this.setData({ editGenderIndex: idx, 'editUser.gender': this.data.genderOptions[idx] });
  },
  // 选择头像（模拟）
  onChooseAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({ 'editUser.avatar': res.tempFilePaths[0] });
      }
    });
  },
  // 保存资料
  onSaveEdit() {
    this.setData({
      user: { ...this.data.editUser },
      showEditModal: false
    });
    wx.showToast({ title: '保存成功', icon: 'success' });
  },
  // 显示已售书籍列表
  onShowSoldBooks() {
    this.setData({ showSoldList: true });
  },
  // 关闭已售书籍列表
  onCloseSoldList() {
    this.setData({ showSoldList: false });
  },
  // 显示收藏书籍列表
  onShowFavBooks() {
    this.setData({ showFavList: true });
  },
  // 关闭收藏书籍列表
  onCloseFavList() {
    this.setData({ showFavList: false });
  },
  // 取消收藏
  onUnfavBook(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({ favBooks: this.data.favBooks.filter(b => b.id !== id) });
    wx.showToast({ title: '已取消收藏', icon: 'success' });
  },
  // 显示浏览记录列表
  onShowHistoryBooks() {
    this.setData({ showHistoryList: true });
  },
  // 关闭浏览记录列表
  onCloseHistoryList() {
    this.setData({ showHistoryList: false });
  },
  // 移除浏览记录
  onRemoveHistoryBook(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({ historyBooks: this.data.historyBooks.filter(b => b.id !== id) });
    wx.showToast({ title: '已移除', icon: 'success' });
  },
  onShowCommunityModal() {
    this.setData({ showCommunityModal: true });
  },
  onCloseCommunityModal() {
    this.setData({ showCommunityModal: false });
  },
  onShowWalletModal() {
    this.setData({ showWalletModal: true });
  },
  onCloseWalletModal() {
    this.setData({ showWalletModal: false });
  },
  onShowOrderModal() {
    this.setData({ showOrderModal: true });
  },
  onCloseOrderModal() {
    this.setData({ showOrderModal: false });
  },
  onShowSettingModal() {
    this.setData({ showSettingModal: true });
  },
  onCloseSettingModal() {
    this.setData({ showSettingModal: false });
  },
  goCommunity() {
    wx.navigateTo({ url: '/pages/community/community' });
  },
  goWallet() {
    wx.navigateTo({ url: '/pages/wallet/wallet' });
  },
  goOrders() {
    wx.navigateTo({ url: '/pages/orders/orders' });
  },
  goSettings() {
    wx.navigateTo({ url: '/pages/settings/settings' });
  },
  // 评分实时计算
  updateScore() {
    let score = 100 - this.data.maliciousComments * 5 - this.data.fakeTrades * 20;
    if (score < 0) score = 0;
    let starCount = 5;
    if (score >= 96) starCount = 5;
    else if (score >= 80) starCount = 4;
    else if (score >= 60) starCount = 3;
    else if (score >= 40) starCount = 2;
    else if (score > 0) starCount = 1;
    else starCount = 0;
    this.setData({ score, starCount });
  },
  // 恶意评论检测（模拟）
  detectMaliciousComment() {
    this.setData({ maliciousComments: this.data.maliciousComments + 1 }, () => {
      this.updateScore();
      this.addMessage('检测到恶意评论，已扣5分');
    });
  },
  // 虚假买卖检测（模拟）
  detectFakeTrade() {
    this.setData({ fakeTrades: this.data.fakeTrades + 1 }, () => {
      this.updateScore();
      this.addMessage('检测到虚假买卖，已扣20分');
    });
  },
  // 添加消息
  addMessage(msg) {
    const messages = this.data.messages || [];
    messages.unshift({ content: msg, time: new Date().toLocaleString() });
    this.setData({ messages });
  },
  // 发布书籍前校验信誉分
  canPublish() {
    this.updateScore();
    return this.data.score >= 60;
  },
  // 评论前校验信誉分
  canComment() {
    this.updateScore();
    return this.data.score >= 80;
  },
  onShow() {
    // 页面显示时刷新学号认证状态
    let auth = false;
    try {
      auth = wx.getStorageSync('studentAuth');
    } catch (e) {}
    this.setData({ studentAuth: !!auth });

    // 读取本地缓存的在售书籍
    let publishedBooks = wx.getStorageSync('publishedBooks') || [];
    this.setData({ onsaleBooks: publishedBooks });

    this.updateScore();
  },
  onLogout() {
    // 清除本地存储的用户信息
    wx.removeStorageSync('user');
    wx.showToast({ title: '已退出登录', icon: 'success', duration: 1000 });
    setTimeout(() => {
      wx.reLaunch({ url: '/pages/login/login' });
    }, 1000);
  },
}) 