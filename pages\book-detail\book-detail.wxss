.book-detail-container {
  background: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.book-detail-header {
  position: relative;
  padding: 24rpx 0 0 0;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}
.book-detail-cover {
  width: 240rpx;
  height: 320rpx;
  border-radius: 12rpx;
  background: #f3f4f6;
  object-fit: contain;
  box-shadow: 0 2rpx 8rpx #e5e7eb;
}
.book-detail-fav-btn {
  position: absolute;
  right: 40rpx;
  top: 40rpx;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx #e5e7eb;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}
.book-detail-main {
  padding: 0 24rpx;
  flex: 1;
}
.book-detail-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 12rpx;
  gap: 24rpx;
}
.book-detail-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #222;
}
.book-detail-price-block {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2rpx;
}
.book-detail-price {
  color: #e53935;
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1;
}
.book-detail-origin-price {
  color: #bdbdbd;
  font-size: 22rpx;
  text-decoration: line-through;
  margin-top: 2rpx;
}
.book-detail-author {
  color: #888;
  font-size: 24rpx;
  margin: 8rpx 0 0 0;
}
.book-detail-meta {
  display: flex;
  gap: 16rpx;
  margin: 8rpx 0;
}
.book-detail-degree {
  background: #dbeafe;
  color: #2176ff;
  border-radius: 8rpx;
  padding: 4rpx 16rpx;
  font-size: 22rpx;
}
.book-detail-type {
  color: #888;
  font-size: 22rpx;
}
.book-detail-stock {
  color: #888;
  font-size: 22rpx;
}
.book-detail-pub {
  color: #888;
  font-size: 22rpx;
  margin-bottom: 8rpx;
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}
.book-detail-desc-block {
  margin: 16rpx 0 0 0;
}
.book-detail-desc-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 8rpx;
  display: block;
}
.book-detail-desc {
  color: #444;
  font-size: 24rpx;
  line-height: 1.7;
}
.book-detail-seller-row {
  display: flex;
  align-items: center;
  margin: 18rpx 0 0 0;
  gap: 16rpx;
}
.book-detail-seller-avatar {
  width: 48rpx;
  height: 48rpx;
  background: #dbeafe;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.seller-avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}
.seller-avatar-default {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2176ff;
  font-size: 26rpx;
  font-weight: bold;
  background: #e6f0ff;
  border-radius: 50%;
}
.book-detail-seller-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}
.book-detail-seller-name {
  font-size: 24rpx;
  color: #222;
  font-weight: bold;
}
.book-detail-seller-major {
  font-size: 22rpx;
  color: #888;
}
.book-detail-seller-stars {
  display: flex;
  align-items: center;
  margin-top: 2rpx;
}
.star-icon {
  color: #ffd600;
  font-size: 22rpx;
  margin-right: 2rpx;
}
.seller-score {
  color: #888;
  font-size: 20rpx;
  margin-left: 6rpx;
}
.contact-icon {
  font-size: 24rpx;
  margin-right: 4rpx;
}
.book-detail-contact-btn {
  background: #2176ff;
  color: #fff;
  border-radius: 24rpx;
  font-size: 24rpx;
  padding: 8rpx 32rpx;
}
.book-detail-recommend-title {
  font-size: 24rpx;
  color: #222;
  font-weight: bold;
  margin: 24rpx 0 8rpx 0;
}
.book-detail-recommend-list {
  display: flex;
  flex-direction: row;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.book-detail-recommend-card {
  width: 180rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx #e5e7eb;
  padding: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16rpx;
}
.book-detail-recommend-cover {
  width: 120rpx;
  height: 160rpx;
  border-radius: 8rpx;
  background: #f3f4f6;
  object-fit: contain;
}
.book-detail-recommend-name {
  font-size: 22rpx;
  color: #222;
  margin: 8rpx 0 0 0;
}
.book-detail-recommend-price {
  color: #e53935;
  font-size: 22rpx;
  font-weight: bold;
}
.book-detail-bottom-bar {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  box-shadow: 0 -2rpx 8rpx #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 16rpx 0;
  z-index: 10;
}
.book-detail-bottom-price-block {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-right: 12rpx;
}
.book-detail-bottom-price {
  color: #e53935;
  font-size: 38rpx;
  font-weight: bold;
  line-height: 1.1;
}
.book-detail-bottom-stock {
  color: #888;
  font-size: 22rpx;
  margin-top: 2rpx;
}
.book-detail-bottom-btn {
  border: none;
  font-size: 26rpx;
  border-radius: 32rpx;
  padding: 16rpx 0;
  width: 180rpx;
  margin: 0 8rpx;
  font-weight: bold;
}
.book-detail-bottom-btn.cart {
  background: #ffd600;
  color: #222;
}
.book-detail-bottom-btn.buy {
  background: #2176ff;
  color: #fff;
}
.book-detail-recommend-list-2col {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx 4%;
  justify-content: flex-start;
  margin-bottom: 24rpx;
}
.book-detail-recommend-card-2col {
  width: 48%;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx #e5e7eb;
  padding: 20rpx 12rpx 16rpx 12rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 0;
  box-sizing: border-box;
  position: relative;
}
.book-detail-recommend-cover-2col {
  width: 120rpx;
  height: 160rpx;
  border-radius: 8rpx;
  background: #f3f4f6;
  object-fit: contain;
  margin-bottom: 12rpx;
  align-self: center;
}
.book-detail-recommend-degree-tag {
  position: absolute;
  right: 16rpx;
  top: 16rpx;
  padding: 0 18rpx;
  height: 36rpx;
  line-height: 36rpx;
  border-radius: 18rpx;
  font-size: 20rpx;
  color: #fff;
  font-weight: 500;
  z-index: 2;
  background: #3b82f6;
}
.book-detail-recommend-degree-tag.purple { background: #a78bfa; }
.book-detail-recommend-degree-tag.red { background: #f87171; }
.book-detail-recommend-degree-tag.green { background: #34d399; }
.book-detail-recommend-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.book-detail-recommend-name {
  font-size: 24rpx;
  color: #222;
  margin-bottom: 2rpx;
  width: 100%;
  white-space: normal;
  font-weight: 500;
}
.book-detail-recommend-author {
  font-size: 20rpx;
  color: #888;
  margin-bottom: 2rpx;
}
.book-detail-recommend-row {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}
.book-detail-recommend-price {
  color: #e53935;
  font-size: 24rpx;
  font-weight: bold;
}
.book-detail-recommend-time {
  color: #bdbdbd;
  font-size: 20rpx;
}
.cart-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cart-dialog {
  background: #fff;
  border-radius: 24rpx;
  min-width: 720rpx;
  max-width: 66vw;
  box-shadow: 0 8rpx 32rpx #b3c6ff44;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: 0 0 32rpx 0;
  animation: popIn .2s;
}
@keyframes popIn {
  0% { transform: scale(0.9); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}
.cart-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0 32rpx;
}
.cart-dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.cart-dialog-close {
  font-size: 40rpx;
  color: #bdbdbd;
  font-weight: normal;
  cursor: pointer;
}
.cart-dialog-content {
  padding: 24rpx 32rpx 0 32rpx;
}
.cart-dialog-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  padding: 18rpx 18rpx 18rpx 18rpx;
  margin-bottom: 12rpx;
}
.cart-dialog-book-cover {
  width: 72rpx;
  height: 96rpx;
  border-radius: 8rpx;
  background: #f3f4f6;
  object-fit: contain;
  margin-right: 18rpx;
}
.cart-dialog-book-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.cart-dialog-book-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 6rpx;
}
.cart-dialog-book-price {
  font-size: 22rpx;
  color: #888;
}
.cart-dialog-book-price-red {
  color: #e53935;
  font-size: 26rpx;
  font-weight: bold;
}
.cart-dialog-remove-btn {
  background: #f3f4f6;
  color: #888;
  border: none;
  border-radius: 12rpx;
  font-size: 22rpx;
  padding: 8rpx 24rpx;
  margin-left: 12rpx;
}
.cart-dialog-footer {
  display: flex;
  flex-direction: column;
  gap: 18rpx;
  padding: 0 32rpx;
  margin-top: 12rpx;
}
.cart-dialog-btn {
  border: none;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: bold;
  padding: 18rpx 0;
  margin: 0;
  width: 100%;
  transition: background 0.2s;
}
.cart-dialog-btn-blue {
  background: #2176ff;
  color: #fff;
}
.cart-dialog-btn-yellow {
  background: #ffd600;
  color: #222;
} 