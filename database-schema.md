# 二手书小程序数据库结构设计

## 1. 用户集合 (users)

```json
{
  "_id": "string", // 微信openid
  "nickname": "string", // 昵称
  "avatar": "string", // 头像云文件ID
  "phone": "string", // 手机号
  "email": "string", // 邮箱
  "gender": "string", // 性别：男/女/保密
  "school": "string", // 学校
  "studentId": "string", // 学号
  "isVerified": "boolean", // 是否学生认证
  "creditScore": "number", // 信誉分，默认100
  "maliciousComments": "number", // 恶意评论次数
  "fakeTrades": "number", // 虚假交易次数
  "createTime": "Date", // 注册时间
  "updateTime": "Date", // 更新时间
  "lastLoginTime": "Date" // 最后登录时间
}
```

## 2. 书籍集合 (books)

```json
{
  "_id": "string", // 自动生成
  "title": "string", // 书名
  "author": "string", // 作者
  "publisher": "string", // 出版社
  "isbn": "string", // ISBN
  "coverImage": "string", // 封面图片云文件ID
  "images": ["string"], // 其他图片云文件ID数组
  "price": "number", // 售价
  "originalPrice": "number", // 原价
  "degree": "string", // 成色：全新/9成新/8成新等
  "category": "string", // 分类：教材/考研资料/小说等
  "description": "string", // 描述
  "stock": "number", // 库存数量
  "delivery": "string", // 交付方式：自取/快递/均可
  "sellerId": "string", // 卖家openid
  "status": "string", // 状态：on_sale/sold/off_shelf
  "viewCount": "number", // 浏览次数
  "favoriteCount": "number", // 收藏次数
  "createTime": "Date", // 发布时间
  "updateTime": "Date" // 更新时间
}
```

## 3. 订单集合 (orders)

```json
{
  "_id": "string", // 自动生成
  "orderNo": "string", // 订单号
  "bookId": "string", // 书籍ID
  "buyerId": "string", // 买家openid
  "sellerId": "string", // 卖家openid
  "quantity": "number", // 购买数量
  "totalPrice": "number", // 总价
  "address": "string", // 收货地址
  "phone": "string", // 联系电话
  "status": "string", // 订单状态：pending/paid/shipped/completed/cancelled
  "paymentMethod": "string", // 支付方式
  "paymentTime": "Date", // 支付时间
  "shipTime": "Date", // 发货时间
  "completeTime": "Date", // 完成时间
  "createTime": "Date", // 创建时间
  "updateTime": "Date" // 更新时间
}
```

## 4. 收藏集合 (favorites)

```json
{
  "_id": "string", // 自动生成
  "userId": "string", // 用户openid
  "bookId": "string", // 书籍ID
  "createTime": "Date" // 收藏时间
}
```

## 5. 浏览记录集合 (viewHistory)

```json
{
  "_id": "string", // 自动生成
  "userId": "string", // 用户openid
  "bookId": "string", // 书籍ID
  "viewTime": "Date" // 浏览时间
}
```

## 6. 社区帖子集合 (posts)

```json
{
  "_id": "string", // 自动生成
  "title": "string", // 标题
  "content": "string", // 内容
  "images": ["string"], // 图片云文件ID数组
  "authorId": "string", // 作者openid
  "category": "string", // 分类
  "likeCount": "number", // 点赞数
  "commentCount": "number", // 评论数
  "viewCount": "number", // 浏览数
  "isTop": "boolean", // 是否置顶
  "status": "string", // 状态：normal/hidden/deleted
  "createTime": "Date", // 创建时间
  "updateTime": "Date" // 更新时间
}
```

## 7. 评论集合 (comments)

```json
{
  "_id": "string", // 自动生成
  "postId": "string", // 帖子ID
  "content": "string", // 评论内容
  "authorId": "string", // 评论者openid
  "parentId": "string", // 父评论ID（回复功能）
  "likeCount": "number", // 点赞数
  "createTime": "Date" // 创建时间
}
```

## 8. 点赞记录集合 (likes)

```json
{
  "_id": "string", // 自动生成
  "userId": "string", // 用户openid
  "targetId": "string", // 目标ID（帖子或评论）
  "targetType": "string", // 目标类型：post/comment
  "createTime": "Date" // 点赞时间
}
```

## 9. 消息集合 (messages)

```json
{
  "_id": "string", // 自动生成
  "userId": "string", // 接收者openid
  "type": "string", // 消息类型：system/trade/comment/like
  "title": "string", // 消息标题
  "content": "string", // 消息内容
  "relatedId": "string", // 关联ID（订单、帖子等）
  "isRead": "boolean", // 是否已读
  "createTime": "Date", // 创建时间
  "updateTime": "Date" // 更新时间
}
```

## 10. 钱包集合 (wallets)

```json
{
  "_id": "string", // 用户openid
  "balance": "number", // 余额
  "totalIncome": "number", // 总收入
  "totalExpense": "number", // 总支出
  "createTime": "Date", // 创建时间
  "updateTime": "Date" // 更新时间
}
```

## 11. 钱包流水集合 (transactions)

```json
{
  "_id": "string", // 自动生成
  "userId": "string", // 用户openid
  "type": "string", // 类型：income/expense
  "amount": "number", // 金额
  "description": "string", // 描述
  "relatedId": "string", // 关联ID（订单等）
  "createTime": "Date" // 创建时间
}
```

## 索引建议

1. users集合：在phone、email字段上建立唯一索引
2. books集合：在sellerId、category、status字段上建立索引
3. orders集合：在buyerId、sellerId、status字段上建立索引
4. favorites集合：在userId、bookId字段上建立复合索引
5. viewHistory集合：在userId、viewTime字段上建立索引
6. posts集合：在authorId、createTime字段上建立索引
7. comments集合：在postId、createTime字段上建立索引
8. messages集合：在userId、isRead字段上建立索引
