const cloud = require('wx-server-sdk')
cloud.init()

const axios = require('axios')

// 获取环境变量
const DASHSCOPE_API_KEY = process.env.DASHSCOPE_API_KEY

// 云函数入口函数
exports.main = async (event, context) => {
  const { name, data } = event

  if (name === 'chat') {
    if (!DASHSCOPE_API_KEY) {
      return { code: 2, msg: '未配置 DASHSCOPE_API_KEY 环境变量' }
    }
    try {
      const response = await axios.post(
        'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
        {
          model: 'deepseek-R1',
          messages: data.messages,
          stream: false
        },
        {
          headers: {
            'Authorization': `Bearer ${DASHSCOPE_API_KEY}`,
            'Content-Type': 'application/json'
          }
        }
      )
      const completion = response.data
      if (!completion.choices || !completion.choices[0] || !completion.choices[0].message) {
        return { code: 3, msg: 'AI接口响应异常', raw: completion }
      }
      return {
        code: 0,
        reasoning: completion.choices[0].message.reasoning_content || '',
        answer: completion.choices[0].message.content,
        raw: completion
      }
    } catch (e) {
      console.error('AI请求失败:', e)
      return {
        code: 1,
        msg: 'AI对话请求失败',
        error: e.toString(),
        stack: e.stack || '',
        detail: e.response ? e.response.data : e
      }
    }
  }

  // 可扩展更多name分支
  return { code: 404, msg: '不支持的请求类型' }
} 