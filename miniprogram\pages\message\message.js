Page({
  data: {
    activeTab: 0,
    msgList: [
      {
        id: 1,
        type: '',
        avatar: '/static/images/avatar1.png',
        nick: '张三',
        time: '10:30',
        title: '高等数学（第七版）',
        desc: '您好，这本书还在吗？我想买',
        unread: 2,
        dialog: [
          { from: 'other', text: '您好，这本书还在吗？我想买' },
          { from: 'me', text: '在的，欢迎购买！' }
        ]
      },
      {
        id: 2,
        type: '',
        avatar: '/static/images/avatar2.png',
        nick: '李四',
        time: '昨天',
        title: '数据结构与算法分析',
        desc: '好的，我们约个时间见面交易吧',
        unread: 0,
        dialog: [
          { from: 'other', text: '好的，我们约个时间见面交易吧' }
        ]
      },
      {
        id: 3,
        type: 'system',
        avatar: '/static/images/bell-blue.png',
        nick: '系统通知',
        time: '3天前',
        title: '您的书籍《高等数学》已成功售出',
        desc: '',
        unread: 0,
        dialog: [
          { from: 'other', text: '您的书籍《高等数学》已成功售出' }
        ]
      },
      {
        id: 4,
        type: 'success',
        avatar: '/static/images/success-green.png',
        nick: '交易成功',
        time: '1周前',
        title: '您已成功购买《线性代数》，请及时确认收货',
        desc: '',
        unread: 0,
        dialog: [
          { from: 'other', text: '您已成功购买《线性代数》，请及时确认收货' }
        ]
      }
    ],
    showDialog: false,
    dialogMsg: {},
    dialogMsgList: [],
    dialogInput: '',
    systemMessages: [],
    activeTabbarIndex: 3
  },
  onTabTap(e) {
    this.setData({ activeTab: Number(e.currentTarget.dataset.index) });
    // 实际开发中可根据tab筛选不同类型消息
  },
  onMsgTap(e) {
    const idx = e.currentTarget.dataset.index;
    const msg = this.data.msgList[idx];
    this.setData({
      showDialog: true,
      dialogMsg: msg,
      dialogMsgList: msg.dialog || [],
      dialogInput: ''
    });
  },
  onCloseDialog() {
    this.setData({ showDialog: false });
  },
  onDialogInput(e) {
    this.setData({ dialogInput: e.detail.value });
  },
  onSendDialog() {
    const text = this.data.dialogInput.trim();
    if (!text) return;
    const list = this.data.dialogMsgList.concat([{ from: 'me', text }]);
    this.setData({ dialogMsgList: list, dialogInput: '' });
    // 可选：模拟对方回复
    setTimeout(() => {
      this.setData({ dialogMsgList: this.data.dialogMsgList.concat([{ from: 'other', text: '收到：' + text }]) });
    }, 1000);
  },
  onShow() {
    // 读取user-center的消息
    const pages = getCurrentPages();
    const userCenterPage = pages.find(p => p.route && p.route.indexOf('user-center/user-center') !== -1);
    if (userCenterPage && userCenterPage.data && userCenterPage.data.messages) {
      this.setData({ systemMessages: userCenterPage.data.messages });
    }
  },
  onTabbarTap(e) {
    const index = Number(e.currentTarget.dataset.index);
    if (index === this.data.activeTabbarIndex) return;
    this.setData({ activeTabbarIndex: index });
    if (index === 0) {
      wx.redirectTo({ url: '/pages/index/index' });
    } else if (index === 4) {
      wx.redirectTo({ url: '/pages/user-center/user-center' });
    } else {
      wx.showToast({ title: '功能开发中', icon: 'none' });
    }
  },
  onTabbarPublish() {
    wx.navigateTo({ url: '/pages/publish/publish' });
  },
  onTabbarMessage() {
    this.setData({ activeTabbarIndex: 3 });
    wx.redirectTo({ url: '/pages/message/message' });
  },
  onTabbarDiscover() {
    this.setData({ activeTabbarIndex: 1 });
    wx.redirectTo({ url: '/pages/discover/discover' });
  }
}); 