const API = require('../../utils/api')

Page({
  data: {
    walletInfo: {
      balance: 0,
      totalIncome: 0,
      totalExpense: 0,
      transactions: []
    },
    loading: true,
    showRechargeModal: false,
    showWithdrawModal: false,
    rechargeAmount: '',
    withdrawAmount: '',
    withdrawAccount: '',
    accountTypeOptions: ['微信', '银行卡'],
    accountTypeIndex: 0,
    transactionPage: 1,
    hasMoreTransactions: true
  },

  onLoad() {
    this.loadWalletInfo();
  },

  onShow() {
    this.loadWalletInfo();
  },

  async loadWalletInfo() {
    try {
      const result = await API.getWalletInfo();
      if (result.success) {
        this.setData({
          walletInfo: result.data,
          loading: false
        });
      } else {
        API.showError(result.message);
      }
    } catch (error) {
      console.error('获取钱包信息失败:', error);
      API.showError('获取钱包信息失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  async loadMoreTransactions() {
    if (!this.data.hasMoreTransactions) return;

    try {
      const result = await API.getTransactions({
        page: this.data.transactionPage + 1,
        pageSize: 10
      });

      if (result.success) {
        const newTransactions = result.data.list;
        if (newTransactions.length > 0) {
          this.setData({
            'walletInfo.transactions': this.data.walletInfo.transactions.concat(newTransactions),
            transactionPage: this.data.transactionPage + 1
          });
        } else {
          this.setData({ hasMoreTransactions: false });
        }
      }
    } catch (error) {
      console.error('加载更多交易记录失败:', error);
    }
  },

  onRecharge() {
    this.setData({ showRechargeModal: true });
  },

  onCloseRechargeModal() {
    this.setData({
      showRechargeModal: false,
      rechargeAmount: ''
    });
  },

  onRechargeInput(e) {
    this.setData({ rechargeAmount: e.detail.value });
  },

  async onConfirmRecharge() {
    const amount = parseFloat(this.data.rechargeAmount);

    if (!amount || amount <= 0) {
      API.showError('请输入正确的充值金额');
      return;
    }

    if (amount > 10000) {
      API.showError('单次充值金额不能超过10000元');
      return;
    }

    try {
      const result = await API.recharge(amount, 'wechat');
      if (result.success) {
        API.showSuccess('充值成功');
        this.setData({
          showRechargeModal: false,
          rechargeAmount: ''
        });
        this.loadWalletInfo(); // 刷新钱包信息
      } else {
        API.showError(result.message);
      }
    } catch (error) {
      console.error('充值失败:', error);
      API.showError('充值失败，请重试');
    }
  },

  onWithdraw() {
    this.setData({ showWithdrawModal: true });
  },

  onCloseWithdrawModal() {
    this.setData({
      showWithdrawModal: false,
      withdrawAmount: '',
      withdrawAccount: ''
    });
  },

  onWithdrawInput(e) {
    this.setData({ withdrawAmount: e.detail.value });
  },

  onWithdrawAccountInput(e) {
    this.setData({ withdrawAccount: e.detail.value });
  },

  onAccountTypeChange(e) {
    this.setData({ accountTypeIndex: e.detail.value });
  },

  async onConfirmWithdraw() {
    const amount = parseFloat(this.data.withdrawAmount);

    if (!amount || amount <= 0) {
      API.showError('请输入正确的提现金额');
      return;
    }

    if (amount > this.data.walletInfo.balance) {
      API.showError('提现金额不能超过余额');
      return;
    }

    if (amount < 1) {
      API.showError('最低提现金额为1元');
      return;
    }

    if (!this.data.withdrawAccount.trim()) {
      API.showError('请输入提现账户信息');
      return;
    }

    try {
      const withdrawData = {
        amount,
        accountType: this.data.accountTypeOptions[this.data.accountTypeIndex] === '微信' ? 'wechat' : 'bank',
        accountInfo: this.data.withdrawAccount.trim()
      };

      const result = await API.withdraw(withdrawData);
      if (result.success) {
        API.showSuccess(result.message);
        this.setData({
          showWithdrawModal: false,
          withdrawAmount: '',
          withdrawAccount: ''
        });
        this.loadWalletInfo(); // 刷新钱包信息
      } else {
        API.showError(result.message);
      }
    } catch (error) {
      console.error('提现失败:', error);
      API.showError('提现失败，请重试');
    }
  },

  onTransactionTap(e) {
    const transaction = e.currentTarget.dataset.transaction;
    if (transaction.relatedId) {
      // 如果有关联订单，跳转到订单详情
      wx.navigateTo({
        url: `/pages/orders/orders?orderId=${transaction.relatedId}`
      });
    }
  },

  onPullDownRefresh() {
    this.loadWalletInfo().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    this.loadMoreTransactions();
  }
})