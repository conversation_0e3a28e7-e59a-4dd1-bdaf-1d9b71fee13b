const API = require('../../utils/api')

Page({
  data: {
    banners: [
      {
        img: '/static/images/banner1.jpg',
        title: '校园二手书交易平台',
        desc: '买卖教材、课外读物，让知识循环起来'
      },
      {
        img: '/static/images/banner2.jpg',
        title: '优质二手书籍',
        desc: '精选校园书籍，品质有保障'
      },
      {
        img: '/static/images/banner3.jpg',
        title: '特价书籍专区',
        desc: '多种优惠，总有一款适合你'
      }
    ],
    categories: [
      { name: '全部', icon: '/static/images/cat0.png', bgClass: 'category-bg-gray' },
      { name: '教材', icon: '/static/images/cat1.png', bgClass: 'category-bg-blue' },
      { name: '考研资料', icon: '/static/images/cat2.png', bgClass: 'category-bg-green' },
      { name: '小说', icon: '/static/images/cat3.png', bgClass: 'category-bg-yellow' },
      { name: '历史', icon: '/static/images/cat4.png', bgClass: 'category-bg-red' },
      { name: '金融', icon: '/static/images/cat5.png', bgClass: 'category-bg-purple' },
      { name: '计算机', icon: '/static/images/cat6.png', bgClass: 'category-bg-orange' }
    ],
    hotBooks: [
      {
        id: 1,
        cover: '/static/images/book1.jpg',
        title: '高等数学（第七版）',
        author: '同济大学数学系',
        price: 25.00,
        desc: '9成新',
        degree: '九成新',
        degreeType: 'blue',
        time: '2025-05-13 18:49',
        type: '教材'
      },
      {
        id: 2,
        cover: '/static/images/book2.jpg',
        title: '数据结构与算法分析',
        author: '王道论坛',
        price: 35.00,
        desc: '8成新',
        degree: '八成新',
        degreeType: 'purple',
        time: '2025-05-13 18:49',
        type: '教材'
      },
      {
        id: 5,
        cover: '/static/images/ai.jpg',
        title: '人工智能',
        author: '周志华',
        price: 42.00,
        desc: '7成新',
        degree: '七成新',
        degreeType: 'red',
        time: '2025-05-13 18:49',
        type: '考研资料'
      },
      {
        id: 6,
        cover: '/static/images/future.jpg',
        title: '未来简史',
        author: '[以色列] 尤瓦尔·赫拉利',
        price: 30.00,
        desc: '8成新',
        degree: '八成新',
        degreeType: 'purple',
        time: '2025-05-13 18:49',
        type: '小说'
      }
    ],
    newBooks: [
      {
        id: 3,
        cover: '/static/images/book3.jpg',
        title: '计算机网络',
        author: '谢希仁',
        price: 30.00,
        desc: '全新',
        degree: '全新',
        degreeType: 'green',
        time: '2025-05-13 18:49',
        type: '教材'
      },
      {
        id: 4,
        cover: '/static/images/book4.jpg',
        title: '操作系统原理',
        author: '汤小丹',
        price: 28.00,
        desc: '全新',
        degree: '全新',
        degreeType: 'green',
        time: '2025-05-13 18:49',
        type: '教材'
      },
      {
        id: 7,
        cover: '/static/images/threebody.jpg',
        title: '三体',
        author: '刘慈欣',
        price: 38.00,
        desc: '9成新',
        degree: '九成新',
        degreeType: 'blue',
        time: '2025-05-13 18:49',
        type: '小说'
      },
      {
        id: 8,
        cover: '/static/images/solitude.jpg',
        title: '百年孤独',
        author: '[哥伦比亚] 加西亚·马尔克斯',
        price: 36.00,
        desc: '8成新',
        degree: '八成新',
        degreeType: 'purple',
        time: '2025-05-13 18:49',
        type: '小说'
      }
    ],
    aiDialogShow: false,
    aiMsgList: [
      { role: 'ai', content: '您好！我是书缘智能助手，有什么可以帮助到您的吗？我可以回答关于平台使用、书籍信息、交易流程等问题。' }
    ],
    aiInput: '',
    aiThinking: false,
    activeTabbarIndex: 0,
    selectedCategory: '全部',
    allHotBooks: [],
    allNewBooks: [],
    showMoreHotDialog: false,
    showMoreNewDialog: false,
  },
  onLoad() {
    this.loadBookData();
  },

  onShow() {
    this.loadBookData();
  },

  async loadBookData() {
    try {
      // 获取热门书籍（按浏览量排序）
      const hotBooksResult = await API.getBookList({
        page: 1,
        pageSize: 10,
        sortBy: 'viewCount'
      });

      // 获取最新书籍（按发布时间排序）
      const newBooksResult = await API.getBookList({
        page: 1,
        pageSize: 10,
        sortBy: 'createTime'
      });

      if (hotBooksResult.success && newBooksResult.success) {
        this.setData({
          hotBooks: hotBooksResult.data.list,
          newBooks: newBooksResult.data.list,
          allHotBooks: hotBooksResult.data.list,
          allNewBooks: newBooksResult.data.list
        });

        // 应用当前分类筛选
        this.applyCategoryFilter();
      } else {
        console.error('获取书籍数据失败');
      }
    } catch (error) {
      console.error('加载书籍数据失败:', error);
    }
  },

  applyCategoryFilter() {
    if (this.data.selectedCategory && this.data.selectedCategory !== '全部') {
      this.setData({
        hotBooks: this.data.allHotBooks.filter(book => book.category === this.data.selectedCategory),
        newBooks: this.data.allNewBooks.filter(book => book.category === this.data.selectedCategory)
      });
    } else {
      this.setData({
        hotBooks: this.data.allHotBooks,
        newBooks: this.data.allNewBooks
      });
    }
  },
  onAIClick() {
    this.setData({ aiDialogShow: true });
    setTimeout(() => {
      this.scrollToAIBottom();
    }, 100);
  },
  onAIClose() {
    this.setData({ aiDialogShow: false });
  },
  onAIInput(e) {
    this.setData({ aiInput: e.detail.value });
  },
  async onAISend() {
    const input = this.data.aiInput.trim();
    if (!input) return wx.showToast({ title: '请输入内容', icon: 'none' });
    const msgList = this.data.aiMsgList.concat([{ role: 'user', content: input }]);
    this.setData({ aiMsgList: msgList, aiInput: '', aiThinking: true });
    this.scrollToAIBottom();
    try {
      const res = await wx.cloud.callFunction({
        name: 'ai-assistant',
        data: {
          name: 'chat',
          data: {
            messages: msgList.map(m => ({ role: m.role === 'ai' ? 'assistant' : m.role, content: m.content }))
          }
        }
      });
      if (res.result && res.result.code === 0) {
        this.setData({
          aiMsgList: this.data.aiMsgList.concat([
            { role: 'ai', content: res.result.answer }
          ]),
          aiThinking: false
        });
        this.scrollToAIBottom();
      } else {
        wx.showToast({ title: res.result.msg || 'AI请求失败', icon: 'none' });
        this.setData({ aiThinking: false });
      }
    } catch (e) {
      wx.showToast({ title: 'AI请求异常', icon: 'none' });
      this.setData({ aiThinking: false });
    }
  },
  scrollToAIBottom() {
    this.setData({ aiScrollInto: 'ai-bottom' });
  },
  onBookTap(e) {
    const bookId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/book-detail/book-detail?id=${bookId}`
    });
  },
  onTabbarTap(e) {
    const index = Number(e.currentTarget.dataset.index);
    if (index === this.data.activeTabbarIndex) return;
    this.setData({ activeTabbarIndex: index });
    // 跳转逻辑
    if (index === 4) {
      wx.navigateTo({ url: '/pages/user-center/user-center' });
    } else if (index === 0) {
      wx.redirectTo({ url: '/pages/index/index' });
    } else {
      wx.showToast({ title: '功能开发中', icon: 'none' });
    }
  },
  onTabbarPublish() {
    wx.navigateTo({ url: '/pages/publish/publish' });
  },
  onTabbarMessage() {
    this.setData({ activeTabbarIndex: 3 });
    wx.navigateTo({ url: '/pages/message/message' });
  },
  onTabbarDiscover() {
    this.setData({ activeTabbarIndex: 1 });
    wx.navigateTo({ url: '/pages/discover/discover' });
  },
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.name;
    this.setData({ selectedCategory: category });
    this.applyCategoryFilter();
  },
  onShowMoreHot() {
    this.setData({ showMoreHotDialog: true });
  },
  onCloseMoreHot() {
    this.setData({ showMoreHotDialog: false });
  },
  onShowMoreNew() {
    this.setData({ showMoreNewDialog: true });
  },
  onCloseMoreNew() {
    this.setData({ showMoreNewDialog: false });
  },
}) 