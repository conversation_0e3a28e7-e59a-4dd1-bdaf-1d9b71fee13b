<view class="container">
  <!-- 顶部标题 -->
  <!-- <view class="header-title">书缘驿站</view> -->

  <!-- 搜索栏 -->
  <view class="search-bar">
    <image src="/static/images/logo-book.png" class="search-logo" />
    <text class="search-title">书缘驿站</text>
    <icon type="search" size="18" color="#bdbdbd" />
    <input class="search-input" placeholder="搜索书籍" />
    <image src="/static/images/filter-blue.png" class="search-filter-icon" />
  </view>

  <!-- 轮播图 -->
  <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
    <block wx:for="{{banners}}" wx:key="index">
      <swiper-item>
        <image src="{{item.img}}" class="banner-img" mode="aspectFill" />
        <view class="banner-text-mask">
          <view class="banner-title">{{item.title}}</view>
          <view class="banner-desc">{{item.desc}}</view>
        </view>
      </swiper-item>
    </block>
  </swiper>

  <!-- 分类 -->
  <view class="category-list">
    <view class="category-item" bindtap="onCategoryTap" data-name="全部">
      <view class="category-icon-bg category-bg-blue">
        <image src="/static/images/cat-all.png" class="category-icon" />
      </view>
      <text class="category-text">全部</text>
    </view>
    <block wx:for="{{categories}}" wx:key="name">
      <view class="category-item" bindtap="onCategoryTap" data-name="{{item.name}}">
        <view class="category-icon-bg {{item.bgClass}}">
          <image src="{{item.icon}}" class="category-icon" />
        </view>
        <text class="category-text">{{item.name}}</text>
      </view>
    </block>
  </view>

  <!-- 热门推荐 -->
  <view class="section">
    <view class="section-title">
      <text>热门推荐</text>
      <button class="more-link" bindtap="onShowMoreHot">查看更多</button>
    </view>
    <view class="book-list">
      <block wx:for="{{hotBooks}}" wx:key="id">
        <view class="book-card" bindtap="onBookTap" data-id="{{item.id}}">
          <view class="book-degree-tag {{'degree-' + item.degreeType}}">{{item.degree}}</view>
          <image src="{{item.cover}}" class="book-cover" mode="aspectFit" />
          <view class="book-title">{{item.title}}</view>
          <view class="book-author">{{item.author}}</view>
          <view class="book-info-row">
            <view class="book-price">￥{{item.price}}</view>
            <view class="book-time">{{item.time}}</view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 最新上架 -->
  <view class="section">
    <view class="section-title">
      <text>最新上架</text>
      <button class="more-link" bindtap="onShowMoreNew">查看更多</button>
    </view>
    <view class="book-list">
      <block wx:for="{{newBooks}}" wx:key="id">
        <view class="book-card" bindtap="onBookTap" data-id="{{item.id}}">
          <view class="book-degree-tag {{'degree-' + item.degreeType}}">{{item.degree}}</view>
          <image src="{{item.cover}}" class="book-cover" mode="aspectFit" />
          <view class="book-title">{{item.title}}</view>
          <view class="book-author">{{item.author}}</view>
          <view class="book-info-row">
            <view class="book-price">￥{{item.price}}</view>
            <view class="book-time">{{item.time}}</view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 悬浮发布按钮 -->
  <button class="fab" open-type="navigate" url="/pages/publish/publish">
    <icon type="add" size="28" color="#fff" />
  </button>

  <!-- 右下角AI助手按钮 -->
  <view class="ai-fab" bindtap="onAIClick">
    <image src="/static/images/ai-bot.png" class="ai-fab-icon" />
  </view>

  <!-- AI助手对话框 -->
  <view wx:if="{{aiDialogShow}}" class="ai-fab-dialog-mask">
    <view class="ai-fab-dialog">
      <view class="ai-fab-dialog-header">
        <image src="/static/images/ai-bot.png" class="ai-fab-dialog-header-icon" />
        <text class="ai-fab-dialog-header-title">书缘智能助手</text>
        <text class="ai-fab-dialog-header-close" bindtap="onAIClose">×</text>
      </view>
      <view class="ai-fab-dialog-msg-list">
        <view class="ai-fab-dialog-msg ai-fab-dialog-msg-ai">
          您好！我是书缘智能助手，有什么可以帮助到您的吗？我可以回答关于平台使用、书籍信息、交易流程等问题。
        </view>
      </view>
      <view class="ai-fab-dialog-inputbar">
        <input class="ai-fab-dialog-input" placeholder="请输入您的问题..." />
        <button class="ai-fab-dialog-sendbtn">
          <image src="/static/images/send.png" class="ai-fab-dialog-sendicon" />
        </button>
      </view>
    </view>
  </view>

  <!-- 底部导航栏 -->
  <view class="tabbar">
    <view class="tabbar-item" bindtap="onTabbarTap" data-index="0">
      <image src="/static/images/tab-home.png" class="tabbar-icon {{activeTabbarIndex === 0 ? 'active' : ''}}" />
      <text class="tabbar-text {{activeTabbarIndex === 0 ? 'active' : ''}}">首页</text>
    </view>
    <view class="tabbar-item" bindtap="onTabbarDiscover" data-index="1">
      <image src="/static/images/tab-discover.png" class="tabbar-icon {{activeTabbarIndex === 1 ? 'active' : ''}}" />
      <text class="tabbar-text {{activeTabbarIndex === 1 ? 'active' : ''}}">发现</text>
    </view>
    <view class="tabbar-item tabbar-item-add" bindtap="onTabbarPublish">
      <image src="/static/images/tab-add.png" class="tabbar-icon tabbar-icon-add tabbar-icon-publish" />
      <text class="tabbar-text tabbar-text-publish">发布</text>
    </view>
    <view class="tabbar-item" bindtap="onTabbarMessage" data-index="3">
      <image src="/static/images/tab-message.png" class="tabbar-icon {{activeTabbarIndex === 3 ? 'active' : ''}}" />
      <text class="tabbar-text {{activeTabbarIndex === 3 ? 'active' : ''}}">消息</text>
    </view>
    <view class="tabbar-item" bindtap="onTabbarTap" data-index="4">
      <image src="/static/images/tab-user.png" class="tabbar-icon {{activeTabbarIndex === 4 ? 'active' : ''}}" />
      <text class="tabbar-text {{activeTabbarIndex === 4 ? 'active' : ''}}">我的</text>
    </view>
  </view>

  <!-- 热门推荐查看更多弹窗 -->
  <view wx:if="{{showMoreHotDialog}}" class="more-dialog-mask">
    <view class="more-dialog">
      <view class="more-dialog-title">全部热门推荐<text class="more-dialog-close" bindtap="onCloseMoreHot">×</text></view>
      <scroll-view scroll-y="true" class="more-dialog-list">
        <block wx:for="{{allHotBooks}}" wx:key="id">
          <view class="book-card" bindtap="onBookTap" data-id="{{item.id}}">
            <view class="book-degree-tag {{'degree-' + item.degreeType}}">{{item.degree}}</view>
            <image src="{{item.cover}}" class="book-cover" mode="aspectFit" />
            <view class="book-title">{{item.title}}</view>
            <view class="book-author">{{item.author}}</view>
            <view class="book-info-row">
              <view class="book-price">￥{{item.price}}</view>
              <view class="book-time">{{item.time}}</view>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
  </view>
  <!-- 最新上架查看更多弹窗 -->
  <view wx:if="{{showMoreNewDialog}}" class="more-dialog-mask">
    <view class="more-dialog">
      <view class="more-dialog-title">全部最新上架<text class="more-dialog-close" bindtap="onCloseMoreNew">×</text></view>
      <scroll-view scroll-y="true" class="more-dialog-list">
        <block wx:for="{{allNewBooks}}" wx:key="id">
          <view class="book-card" bindtap="onBookTap" data-id="{{item.id}}">
            <view class="book-degree-tag {{'degree-' + item.degreeType}}">{{item.degree}}</view>
            <image src="{{item.cover}}" class="book-cover" mode="aspectFit" />
            <view class="book-title">{{item.title}}</view>
            <view class="book-author">{{item.author}}</view>
            <view class="book-info-row">
              <view class="book-price">￥{{item.price}}</view>
              <view class="book-time">{{item.time}}</view>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
  </view>
</view> 