Page({
  data: {
    posts: [
      { id: 1, avatar: '/static/images/avatar1.png', nickname: '小明', time: '1小时前', content: '这本书真的很棒！', likes: 2, comments: [ {cid: 1, avatar: '/static/images/avatar2.png', nickname: '小红', content: '同感！'} ], images: ['/static/images/post1.jpg'] },
      { id: 2, avatar: '/static/images/avatar2.png', nickname: '小李', time: '2小时前', content: '有推荐的考研书吗？', likes: 1, comments: [], images: ['/static/images/post2.jpg', '/static/images/post3.jpg'] },
      { id: 3, avatar: '/static/images/avatar3.png', nickname: '小王', time: '3小时前', content: '二手教材很划算！', likes: 0, comments: [], images: [] }
    ],
    showPostModal: false,
    postNickname: '',
    postContent: '',
    postImages: [],
    commentingId: null,
    commentInput: ''
  },
  onShowPostModal() { this.setData({ showPostModal: true }); },
  onClosePostModal() { this.setData({ showPostModal: false, postImages: [] }); },
  onNicknameInput(e) { this.setData({ postNickname: e.detail.value }); },
  onContentInput(e) { this.setData({ postContent: e.detail.value }); },
  onChooseImage() {
    wx.chooseImage({
      count: 3 - this.data.postImages.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({ postImages: this.data.postImages.concat(res.tempFilePaths) });
      }
    });
  },
  onPost() {
    if (!this.data.postNickname || !this.data.postContent) {
      wx.showToast({ title: '请填写完整', icon: 'none' }); return;
    }
    const newPost = {
      id: Date.now(),
      avatar: '/static/images/avatar1.png',
      nickname: this.data.postNickname,
      time: '刚刚',
      content: this.data.postContent,
      likes: 0,
      comments: [],
      images: this.data.postImages
    };
    this.setData({
      posts: [newPost, ...this.data.posts],
      showPostModal: false,
      postNickname: '',
      postContent: '',
      postImages: []
    });
    wx.showToast({ title: '发布成功', icon: 'success' });
  },
  onLike(e) {
    const id = e.currentTarget.dataset.id;
    const posts = this.data.posts.map(post => post.id === id ? { ...post, likes: post.likes + 1 } : post);
    this.setData({ posts });
  },
  onComment(e) {
    this.setData({ commentingId: e.currentTarget.dataset.id, commentInput: '' });
  },
  onCommentInput(e) {
    this.setData({ commentInput: e.detail.value });
  },
  onSendComment() {
    const id = this.data.commentingId;
    if (!this.data.commentInput) return wx.showToast({ title: '请输入评论', icon: 'none' });
    const posts = this.data.posts.map(post => {
      if (post.id === id) {
        const comments = post.comments || [];
        comments.push({ cid: Date.now(), avatar: '/static/images/avatar1.png', nickname: '我', content: this.data.commentInput });
        return { ...post, comments };
      }
      return post;
    });
    this.setData({ posts, commentingId: null, commentInput: '' });
    wx.showToast({ title: '评论成功', icon: 'success' });
  },
  onShare(e) {
    wx.showToast({ title: '已转发', icon: 'success' });
  }
})
// 头像图片命名建议：avatar1.png、avatar2.png、avatar3.png，放在/static/images/目录下
// 帖子图片命名建议：post1.jpg、post2.jpg、post3.jpg，放在/static/images/目录下 