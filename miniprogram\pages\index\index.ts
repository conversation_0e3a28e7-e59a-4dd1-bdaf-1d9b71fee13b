Page({
  data: {
    banners: [
      '/static/images/banner1.jpg',
      '/static/images/banner2.jpg',
      '/static/images/banner3.jpg'
    ],
    categories: [
      { name: '教材', icon: '/static/images/cat1.png' },
      { name: '考研资料', icon: '/static/images/cat2.png' },
      { name: '小说', icon: '/static/images/cat3.png' },
      { name: '历史', icon: '/static/images/cat4.png' },
      { name: '金融', icon: '/static/images/cat5.png' }
    ],
    hotBooks: [
      {
        id: 1,
        cover: '/static/images/book1.jpg',
        title: '高等数学（第七版）',
        price: 25.00,
        desc: '9成新'
      },
      {
        id: 2,
        cover: '/static/images/book2.jpg',
        title: '数据结构与算法分析',
        price: 35.00,
        desc: '8成新'
      }
    ],
    newBooks: [
      {
        id: 3,
        cover: '/static/images/book3.jpg',
        title: '计算机网络',
        price: 30.00,
        desc: '全新'
      },
      {
        id: 4,
        cover: '/static/images/book4.jpg',
        title: '操作系统原理',
        price: 28.00,
        desc: '全新'
      }
    ]
  },
  onLoad() {
    // 可在此处请求后端API获取数据，当前为静态数据
  }
}) 