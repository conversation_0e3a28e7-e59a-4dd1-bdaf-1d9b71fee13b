const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})
const db = cloud.database()
const _ = db.command

// 用户信息管理
exports.main = async (event, context) => {
  const { type, data } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  switch (type) {
    // 获取用户信息
    case 'getUserInfo':
      try {
        const result = await db.collection('users').doc(openid).get()
        if (result.data) {
          return {
            code: 0,
            data: result.data,
            message: '获取成功'
          }
        } else {
          // 用户不存在，创建新用户
          const newUser = {
            _id: openid,
            nickname: '新用户',
            avatar: '',
            phone: '',
            email: '',
            gender: '保密',
            school: '',
            studentId: '',
            isVerified: false,
            creditScore: 100,
            maliciousComments: 0,
            fakeTrades: 0,
            createTime: db.serverDate(),
            updateTime: db.serverDate(),
            lastLoginTime: db.serverDate()
          }
          await db.collection('users').add({ data: newUser })
          return {
            code: 0,
            data: newUser,
            message: '用户创建成功'
          }
        }
      } catch (error) {
        return {
          code: -1,
          message: '获取用户信息失败',
          error: error.message
        }
      }

    // 更新用户信息
    case 'updateUserInfo':
      try {
        await db.collection('users').doc(openid).update({
          data: {
            ...data,
            updateTime: db.serverDate()
          }
        })
        return {
          code: 0,
          message: '更新成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '更新失败',
          error: error.message
        }
      }

    // 学生认证
    case 'verifyStudent':
      try {
        await db.collection('users').doc(openid).update({
          data: {
            studentId: data.studentId,
            school: data.school,
            isVerified: true,
            updateTime: db.serverDate()
          }
        })
        return {
          code: 0,
          message: '认证成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '认证失败',
          error: error.message
        }
      }
    
    // 添加浏览记录
    case 'addViewHistory':
      try {
        // 检查是否已存在相同记录，避免重复添加
        const existing = await db.collection('viewHistory')
          .where({
            userId: openid,
            bookId: data.bookId
          })
          .get()

        if (existing.data.length > 0) {
          // 更新浏览时间
          await db.collection('viewHistory').doc(existing.data[0]._id).update({
            data: {
              viewTime: db.serverDate()
            }
          })
        } else {
          // 添加新记录
          await db.collection('viewHistory').add({
            data: {
              userId: openid,
              bookId: data.bookId,
              viewTime: db.serverDate()
            }
          })
        }

        // 更新书籍浏览次数
        await db.collection('books').doc(data.bookId).update({
          data: {
            viewCount: _.inc(1)
          }
        })

        return {
          code: 0,
          message: '记录成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '记录失败',
          error: error.message
        }
      }

    // 获取浏览记录
    case 'getViewHistory':
      try {
        const result = await db.collection('viewHistory')
          .where({
            userId: openid
          })
          .orderBy('viewTime', 'desc')
          .limit(50)
          .get()

        return {
          code: 0,
          data: result.data,
          message: '获取成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '获取失败',
          error: error.message
        }
      }
    
    // 添加收藏
    case 'addFavorite':
      try {
        // 检查是否已收藏
        const existing = await db.collection('favorites')
          .where({
            userId: openid,
            bookId: data.bookId
          })
          .get()

        if (existing.data.length > 0) {
          return {
            code: -1,
            message: '已收藏过该书籍'
          }
        }

        await db.collection('favorites').add({
          data: {
            userId: openid,
            bookId: data.bookId,
            createTime: db.serverDate()
          }
        })

        // 更新书籍收藏次数
        await db.collection('books').doc(data.bookId).update({
          data: {
            favoriteCount: _.inc(1)
          }
        })

        return {
          code: 0,
          message: '收藏成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '收藏失败',
          error: error.message
        }
      }

    // 取消收藏
    case 'removeFavorite':
      try {
        const result = await db.collection('favorites')
          .where({
            userId: openid,
            bookId: data.bookId
          })
          .remove()

        if (result.stats.removed > 0) {
          // 更新书籍收藏次数
          await db.collection('books').doc(data.bookId).update({
            data: {
              favoriteCount: _.inc(-1)
            }
          })

          return {
            code: 0,
            message: '取消收藏成功'
          }
        } else {
          return {
            code: -1,
            message: '未找到收藏记录'
          }
        }
      } catch (error) {
        return {
          code: -1,
          message: '取消收藏失败',
          error: error.message
        }
      }

    // 获取收藏列表
    case 'getFavorites':
      try {
        const result = await db.collection('favorites')
          .where({
            userId: openid
          })
          .orderBy('createTime', 'desc')
          .get()

        return {
          code: 0,
          data: result.data,
          message: '获取成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '获取失败',
          error: error.message
        }
      }

    // 用户注册
    case 'register':
      try {
        const { phone, password, nickname, email, gender } = data

        // 数据验证
        if (!phone || !password || !nickname) {
          return {
            code: 400,
            message: '手机号、密码和昵称不能为空'
          }
        }

        // 手机号格式验证
        const phoneRegex = /^1[3-9]\d{9}$/
        if (!phoneRegex.test(phone)) {
          return {
            code: 400,
            message: '手机号格式不正确'
          }
        }

        // 邮箱格式验证（如果提供）
        if (email) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(email)) {
            return {
              code: 400,
              message: '邮箱格式不正确'
            }
          }
        }

        // 检查手机号是否已注册
        const existUser = await db.collection('users')
          .where({ phone })
          .get()

        if (existUser.data.length > 0) {
          return {
            code: 409,
            message: '该手机号已注册'
          }
        }

        // 检查昵称是否已存在
        const existNickname = await db.collection('users')
          .where({ nickname })
          .get()

        if (existNickname.data.length > 0) {
          return {
            code: 409,
            message: '该昵称已被使用'
          }
        }

        // 创建新用户
        const userData = {
          _id: openid,
          phone,
          password, // 注意：实际项目中应该加密存储密码
          nickname,
          email: email || '',
          gender: gender || '保密',
          avatar: '',
          school: '',
          studentId: '',
          isVerified: false,
          creditScore: 100,
          maliciousComments: 0,
          fakeTrades: 0,
          createTime: db.serverDate(),
          updateTime: db.serverDate(),
          lastLoginTime: db.serverDate()
        }

        await db.collection('users').add({ data: userData })

        // 创建用户钱包
        await db.collection('wallets').add({
          data: {
            _id: openid,
            balance: 0,
            totalIncome: 0,
            totalExpense: 0,
            createTime: db.serverDate(),
            updateTime: db.serverDate()
          }
        })

        return {
          code: 0,
          data: { userId: openid },
          message: '注册成功'
        }
      } catch (error) {
        return {
          code: -1,
          message: '注册失败',
          error: error.message
        }
      }

    // 获取已售书籍
    case 'getSoldBooks':
      return await db.collection('books')
        .where({
          sellerId: openid,
          status: 'sold'
        })
        .orderBy('createTime', 'desc')
        .get()
    
    // 获取用户消息
    case 'getMessages':
      return await db.collection('messages')
        .where({
          userId: openid
        })
        .orderBy('createTime', 'desc')
        .get()
    
    // 更新消息状态
    case 'updateMessageStatus':
      return await db.collection('messages')
        .doc(data.messageId)
        .update({
          data: {
            isRead: true,
            updateTime: db.serverDate()
          }
        })
  }
} 