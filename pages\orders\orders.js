const API = require('../../utils/api')

Page({
  data: {
    orders: [],
    loading: true,
    activeTab: 'all', // all, buy, sell
    statusFilter: '', // pending, paid, shipped, completed, cancelled
    page: 1,
    hasMore: true,
    showOrderDetailModal: false,
    orderDetail: {},
    statusMap: {
      'pending': '待支付',
      'paid': '已支付',
      'shipped': '已发货',
      'completed': '已完成',
      'cancelled': '已取消'
    }
  },

  onLoad(options) {
    // 如果有指定订单ID，直接显示该订单详情
    if (options.orderId) {
      this.loadOrderDetail(options.orderId);
    } else {
      this.loadOrders();
    }
  },

  onShow() {
    // 刷新订单列表
    this.setData({ page: 1, hasMore: true });
    this.loadOrders();
  },

  async loadOrders() {
    try {
      this.setData({ loading: true });

      const params = {
        page: this.data.page,
        pageSize: 10
      };

      if (this.data.activeTab !== 'all') {
        params.type = this.data.activeTab;
      }

      if (this.data.statusFilter) {
        params.status = this.data.statusFilter;
      }

      const result = await API.getOrderList(params);

      if (result.success) {
        const newOrders = result.data.list;
        this.setData({
          orders: this.data.page === 1 ? newOrders : this.data.orders.concat(newOrders),
          hasMore: newOrders.length === 10,
          loading: false
        });
      } else {
        API.showError(result.message);
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error('获取订单列表失败:', error);
      API.showError('获取订单列表失败');
      this.setData({ loading: false });
    }
  },

  async loadOrderDetail(orderId) {
    try {
      // 这里可以添加获取单个订单详情的API调用
      // 暂时从订单列表中查找
      const orders = this.data.orders;
      const order = orders.find(o => o._id === orderId);
      if (order) {
        this.setData({
          orderDetail: order,
          showOrderDetailModal: true
        });
      }
    } catch (error) {
      console.error('获取订单详情失败:', error);
    }
  },

  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab,
      page: 1,
      hasMore: true
    });
    this.loadOrders();
  },

  onStatusFilterChange(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      statusFilter: status === this.data.statusFilter ? '' : status,
      page: 1,
      hasMore: true
    });
    this.loadOrders();
  },

  onShowOrderDetail(e) {
    const order = e.currentTarget.dataset.order;
    this.setData({
      showOrderDetailModal: true,
      orderDetail: order
    });
  },

  onCloseOrderDetailModal() {
    this.setData({
      showOrderDetailModal: false,
      orderDetail: {}
    });
  },

  async onPayOrder(e) {
    const orderId = e.currentTarget.dataset.orderId;

    try {
      // 跳转到支付页面
      wx.navigateTo({
        url: `/pages/pay/pay?orderId=${orderId}`
      });
    } catch (error) {
      console.error('跳转支付页面失败:', error);
      API.showError('跳转支付页面失败');
    }
  },

  async onCancelOrder(e) {
    const orderId = e.currentTarget.dataset.orderId;

    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await API.call('order', 'cancelOrder', { orderId });
            if (result.success) {
              API.showSuccess('订单已取消');
              this.loadOrders(); // 刷新订单列表
            } else {
              API.showError(result.message);
            }
          } catch (error) {
            console.error('取消订单失败:', error);
            API.showError('取消订单失败');
          }
        }
      }
    });
  },

  async onConfirmReceived(e) {
    const orderId = e.currentTarget.dataset.orderId;

    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await API.updateOrderStatus(orderId, 'completed');
            if (result.success) {
              API.showSuccess('确认收货成功');
              this.loadOrders(); // 刷新订单列表
            } else {
              API.showError(result.message);
            }
          } catch (error) {
            console.error('确认收货失败:', error);
            API.showError('确认收货失败');
          }
        }
      }
    });
  },

  async onShipOrder(e) {
    const orderId = e.currentTarget.dataset.orderId;

    wx.showModal({
      title: '确认发货',
      content: '确认已发货吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await API.updateOrderStatus(orderId, 'shipped');
            if (result.success) {
              API.showSuccess('发货成功');
              this.loadOrders(); // 刷新订单列表
            } else {
              API.showError(result.message);
            }
          } catch (error) {
            console.error('发货失败:', error);
            API.showError('发货失败');
          }
        }
      }
    });
  },

  onContactUser(e) {
    const userId = e.currentTarget.dataset.userId;
    const userType = e.currentTarget.dataset.userType; // buyer 或 seller

    wx.showModal({
      title: `联系${userType === 'buyer' ? '买家' : '卖家'}`,
      content: '联系功能开发中',
      showCancel: false
    });
  },

  onPullDownRefresh() {
    this.setData({ page: 1, hasMore: true });
    this.loadOrders().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({ page: this.data.page + 1 });
      this.loadOrders();
    }
  }
})