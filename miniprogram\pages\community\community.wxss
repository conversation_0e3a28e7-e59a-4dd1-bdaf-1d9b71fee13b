.community-container { min-height: 100vh; background: #f5f6fa; }
.community-header { display: flex; justify-content: space-between; align-items: center; padding: 32rpx; }
.community-title { font-size: 52rpx; font-weight: bold; color: #2176ff; }
.community-post-btn { background: #2176ff; color: #fff; border-radius: 32rpx; font-size: 36rpx; padding: 18rpx 60rpx; }
.community-list { padding: 0 32rpx; }
.community-post-card { display: flex; background: #fff; border-radius: 32rpx; box-shadow: 0 4rpx 16rpx #e5e7eb22; margin-bottom: 40rpx; padding: 36rpx; }
.avatar { width: 96rpx; height: 96rpx; border-radius: 50%; margin-right: 32rpx; }
.post-main { flex: 1; }
.post-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12rpx; }
.nickname { font-weight: bold; color: #222; font-size: 36rpx; }
.time { color: #aaa; font-size: 26rpx; }
.content { color: #333; font-size: 36rpx; margin-bottom: 18rpx; }
.post-actions { display: flex; gap: 18rpx; margin-bottom: 12rpx; }
.action-btn { background: #f5f6fa; color: #2176ff; border-radius: 24rpx; font-size: 26rpx; padding: 0 22rpx; border: none; height: 48rpx; min-width: 80rpx; display: flex; align-items: center; justify-content: center; }
.post-images { display: flex; gap: 12rpx; margin: 12rpx 0; }
.post-image { width: 120rpx; height: 120rpx; border-radius: 12rpx; object-fit: cover; background: #eee; }
.post-comments { margin-top: 10rpx; }
.comment-item { display: flex; align-items: center; margin-bottom: 8rpx; }
.comment-avatar { width: 44rpx; height: 44rpx; border-radius: 50%; margin-right: 12rpx; }
.comment-nickname { color: #2176ff; font-size: 26rpx; font-weight: 600; }
.comment-content { color: #333; font-size: 26rpx; }
.comment-input-bar { display: flex; align-items: center; margin-top: 12rpx; }
.comment-input { flex: 1; border-radius: 16rpx; border: 1.5rpx solid #e5e7eb; background: #f7faff; font-size: 28rpx; color: #222; padding: 0 18rpx; height: 56rpx; margin-right: 12rpx; }
.comment-send-btn { background: linear-gradient(90deg, #2176ff 0%, #5faaff 100%); color: #fff; border-radius: 16rpx; font-size: 28rpx; padding: 0 32rpx; border: none; height: 56rpx; }
.modal-mask { position: fixed; left: 0; top: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.18); z-index: 200; display: flex; align-items: center; justify-content: center; }
.modal { width: 90vw; max-width: 650rpx; background: #fff; border-radius: 36rpx; box-shadow: 0 12rpx 48rpx #2176ff33; padding: 56rpx 36rpx 40rpx 36rpx; display: flex; flex-direction: column; align-items: center; position: relative; }
.modal-title { font-size: 44rpx; font-weight: bold; color: #2176ff; margin-bottom: 36rpx; width: 100%; text-align: left; }
.modal-close { position: absolute; right: 36rpx; top: 56rpx; font-size: 54rpx; color: #bbb; cursor: pointer; }
.modal-input, .modal-textarea { width: 100%; border-radius: 18rpx; border: 1.5rpx solid #e5e7eb; background: #f7faff; font-size: 34rpx; color: #222; margin-bottom: 28rpx; padding: 0 24rpx; }
.modal-textarea { min-height: 120rpx; padding: 16rpx 24rpx; }
.modal-btn { width: 100%; height: 80rpx; background: linear-gradient(90deg, #2176ff 0%, #5faaff 100%); color: #fff; border-radius: 22rpx; font-size: 38rpx; font-weight: bold; border: none; margin-top: 18rpx; } 