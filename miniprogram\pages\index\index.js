Page({
  data: {
    banners: [
      {
        img: '/static/images/banner1.jpg',
        title: '校园二手书交易平台',
        desc: '买卖教材、课外读物，让知识循环起来'
      },
      {
        img: '/static/images/banner2.jpg',
        title: '优质二手书籍',
        desc: '精选校园书籍，品质有保障'
      },
      {
        img: '/static/images/banner3.jpg',
        title: '特价书籍专区',
        desc: '多种优惠，总有一款适合你'
      }
    ],
    categories: [
      { name: '教材', icon: '/static/images/cat1.png', bgClass: 'category-bg-blue' },
      { name: '考研资料', icon: '/static/images/cat2.png', bgClass: 'category-bg-green' },
      { name: '小说', icon: '/static/images/cat3.png', bgClass: 'category-bg-yellow' },
      { name: '历史', icon: '/static/images/cat4.png', bgClass: 'category-bg-red' },
      { name: '金融', icon: '/static/images/cat5.png', bgClass: 'category-bg-purple' }
    ],
    hotBooks: [
      {
        id: 1,
        cover: '/static/images/book1.jpg',
        title: '高等数学（第七版）',
        author: '同济大学数学系',
        price: 25.00,
        desc: '9成新',
        degree: '九成新',
        degreeType: 'blue',
        time: '2025-05-13 18:49',
        type: '教材'
      },
      {
        id: 2,
        cover: '/static/images/book2.jpg',
        title: '数据结构与算法分析',
        author: '王道论坛',
        price: 35.00,
        desc: '8成新',
        degree: '八成新',
        degreeType: 'purple',
        time: '2025-05-13 18:49',
        type: '教材'
      },
      {
        id: 5,
        cover: '/static/images/ai.jpg',
        title: '人工智能',
        author: '周志华',
        price: 42.00,
        desc: '7成新',
        degree: '七成新',
        degreeType: 'red',
        time: '2025-05-13 18:49',
        type: '考研资料'
      },
      {
        id: 6,
        cover: '/static/images/future.jpg',
        title: '未来简史',
        author: '[以色列] 尤瓦尔·赫拉利',
        price: 30.00,
        desc: '8成新',
        degree: '八成新',
        degreeType: 'purple',
        time: '2025-05-13 18:49',
        type: '小说'
      }
    ],
    newBooks: [
      {
        id: 3,
        cover: '/static/images/book3.jpg',
        title: '计算机网络',
        author: '谢希仁',
        price: 30.00,
        desc: '全新',
        degree: '全新',
        degreeType: 'green',
        time: '2025-05-13 18:49',
        type: '教材'
      },
      {
        id: 4,
        cover: '/static/images/book4.jpg',
        title: '操作系统原理',
        author: '汤小丹',
        price: 28.00,
        desc: '全新',
        degree: '全新',
        degreeType: 'green',
        time: '2025-05-13 18:49',
        type: '教材'
      },
      {
        id: 7,
        cover: '/static/images/threebody.jpg',
        title: '三体',
        author: '刘慈欣',
        price: 38.00,
        desc: '9成新',
        degree: '九成新',
        degreeType: 'blue',
        time: '2025-05-13 18:49',
        type: '小说'
      },
      {
        id: 8,
        cover: '/static/images/solitude.jpg',
        title: '百年孤独',
        author: '[哥伦比亚] 加西亚·马尔克斯',
        price: 36.00,
        desc: '8成新',
        degree: '八成新',
        degreeType: 'purple',
        time: '2025-05-13 18:49',
        type: '小说'
      }
    ],
    aiDialogShow: false,
    activeTabbarIndex: 0,
    selectedCategory: '全部',
    allHotBooks: [],
    allNewBooks: [],
    showMoreHotDialog: false,
    showMoreNewDialog: false,
  },
  onLoad() {
    // 记录原始数据，便于筛选
    this.setData({
      allHotBooks: this.data.hotBooks,
      allNewBooks: this.data.newBooks
    });
  },
  onShow() {
    // 读取本地缓存的发布书籍
    const publishedBooks = wx.getStorageSync('publishedBooks') || [];
    // 合并到hotBooks和newBooks（最新发布的优先）
    const allHotBooks = [...publishedBooks, ...this.data.hotBooks.filter(b => !publishedBooks.find(pb => pb.id === b.id))];
    const allNewBooks = [...publishedBooks, ...this.data.newBooks.filter(b => !publishedBooks.find(pb => pb.id === b.id))];
    this.setData({
      hotBooks: allHotBooks,
      newBooks: allNewBooks,
      allHotBooks,
      allNewBooks
    });
    // 分类筛选同步
    if (this.data.selectedCategory && this.data.selectedCategory !== '全部') {
      this.setData({
        hotBooks: allHotBooks.filter(book => book.type === this.data.selectedCategory),
        newBooks: allNewBooks.filter(book => book.type === this.data.selectedCategory)
      });
    }
  },
  onAIClick() {
    this.setData({ aiDialogShow: true });
  },
  onAIClose() {
    this.setData({ aiDialogShow: false });
  },
  onBookTap(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/book-detail/book-detail?id=${id}`
    });
  },
  onTabbarTap(e) {
    const index = Number(e.currentTarget.dataset.index);
    if (index === this.data.activeTabbarIndex) return;
    this.setData({ activeTabbarIndex: index });
    // 跳转逻辑
    if (index === 4) {
      wx.navigateTo({ url: '/pages/user-center/user-center' });
    } else if (index === 0) {
      wx.redirectTo({ url: '/pages/index/index' });
    } else {
      wx.showToast({ title: '功能开发中', icon: 'none' });
    }
  },
  onTabbarPublish() {
    wx.navigateTo({ url: '/pages/publish/publish' });
  },
  onTabbarMessage() {
    this.setData({ activeTabbarIndex: 3 });
    wx.navigateTo({ url: '/pages/message/message' });
  },
  onTabbarDiscover() {
    this.setData({ activeTabbarIndex: 1 });
    wx.navigateTo({ url: '/pages/discover/discover' });
  },
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.name;
    this.setData({ selectedCategory: category });
    // 这里假设书籍有type字段，实际应与后端/数据结构一致
    if (category === '全部') {
      this.setData({
        hotBooks: this.data.allHotBooks,
        newBooks: this.data.allNewBooks
      });
    } else {
      this.setData({
        hotBooks: this.data.allHotBooks.filter(book => book.type === category),
        newBooks: this.data.allNewBooks.filter(book => book.type === category)
      });
    }
  },
  onShowMoreHot() {
    this.setData({ showMoreHotDialog: true });
  },
  onCloseMoreHot() {
    this.setData({ showMoreHotDialog: false });
  },
  onShowMoreNew() {
    this.setData({ showMoreNewDialog: true });
  },
  onCloseMoreNew() {
    this.setData({ showMoreNewDialog: false });
  },
}) 