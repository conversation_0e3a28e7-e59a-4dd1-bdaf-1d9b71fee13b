<view class="book-detail-container">
  <view class="book-detail-header">
    <image class="book-detail-cover" src="{{book.cover}}" mode="aspectFit" alt="书籍封面" />
    <view class="book-detail-fav-btn">
      <icon type="heart" size="32" color="#2176ff" />
    </view>
  </view>
  <view class="book-detail-main">
    <view class="book-detail-title-row">
      <text class="book-detail-title">{{book.title}}</text>
      <view class="book-detail-price-block">
        <text class="book-detail-price">￥{{book.price}}</text>
        <text class="book-detail-origin-price">原价：￥{{book.originPrice}}</text>
      </view>
    </view>
    <view class="book-detail-author">{{book.author}}</view>
    <view class="book-detail-meta">
      <text class="book-detail-degree">{{book.degree}}</text>
      <text class="book-detail-type">{{book.type}}</text>
      <text class="book-detail-stock">库存：{{book.stock}}本</text>
    </view>
    <view class="book-detail-pub">
      <text>出版社：{{book.pub}}</text>
      <text>ISBN：{{book.isbn}}</text>
      <text>出版时间：{{book.pubDate}}</text>
    </view>
    <view class="book-detail-desc-block">
      <text class="book-detail-desc-title">书籍描述</text>
      <text class="book-detail-desc">{{book.desc}}</text>
    </view>
    <view class="book-detail-seller-row">
      <view class="book-detail-seller-avatar">
        <image wx:if="{{book.seller.avatar}}" src="{{book.seller.avatar}}" class="seller-avatar-img" mode="aspectFill" />
        <view wx:else class="seller-avatar-default">{{book.seller.name ? book.seller.name[0] : 'U'}}</view>
      </view>
      <view class="book-detail-seller-info">
        <text class="book-detail-seller-name">{{book.seller.name}}</text>
        <text class="book-detail-seller-major">{{book.seller.major}}</text>
        <view class="book-detail-seller-stars">
          <block wx:for="{{[1,2,3,4,5]}}" wx:key="index">
            <text class="star-icon">★</text>
          </block>
          <text class="seller-score">100分</text>
        </view>
      </view>
      <button class="book-detail-contact-btn" bindtap="handleContactSeller">
        <text class="contact-icon">💬</text> 联系卖家
      </button>
    </view>
    <view class="book-detail-recommend-title">相似推荐</view>
    <view class="book-detail-recommend-list-2col">
      <block wx:for="{{recommend}}" wx:key="id">
        <view class="book-detail-recommend-card-2col">
          <image class="book-detail-recommend-cover-2col" src="{{item.cover}}" mode="aspectFit" />
          <view class="book-detail-recommend-degree-tag {{item.degreeType ? item.degreeType : ''}}">{{item.degree}}</view>
          <view class="book-detail-recommend-info">
            <text class="book-detail-recommend-name">{{item.title}}</text>
            <text class="book-detail-recommend-author">{{item.author}}</text>
            <view class="book-detail-recommend-row">
              <text class="book-detail-recommend-price">￥{{item.price}}</text>
              <text class="book-detail-recommend-time">{{item.time}}</text>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
  <view class="book-detail-bottom-bar">
    <view class="book-detail-bottom-price-block">
      <view class="book-detail-bottom-price">￥{{book.price}}</view>
      <view class="book-detail-bottom-stock">库存：{{book.stock}}本</view>
    </view>
    <button class="book-detail-bottom-btn cart" bindtap="handleAddCart">加入购物车</button>
    <button class="book-detail-bottom-btn buy" bindtap="handleBuy">立即购买</button>
  </view>
</view>

<view wx:if="{{showCartDialog}}" class="cart-dialog-mask">
  <view class="cart-dialog">
    <view class="cart-dialog-header">
      <text class="cart-dialog-title">{{cartDialogType === 'add' ? '已加入购物车' : '我的购物车'}}</text>
      <text class="cart-dialog-close" bindtap="closeCartDialog">×</text>
    </view>
    <view class="cart-dialog-content">
      <view class="cart-dialog-item">
        <image class="cart-dialog-book-cover" src="{{book.cover}}" mode="aspectFit" />
        <view class="cart-dialog-book-info">
          <text class="cart-dialog-book-title">{{book.title}}</text>
          <text class="cart-dialog-book-price">价格：<text class="cart-dialog-book-price-red">￥{{book.price}}</text></text>
        </view>
        <button wx:if="{{cartDialogType === 'cart'}}" class="cart-dialog-remove-btn" bindtap="removeFromCart">移除</button>
      </view>
    </view>
    <view class="cart-dialog-footer">
      <button wx:if="{{cartDialogType === 'add'}}" class="cart-dialog-btn cart-dialog-btn-blue" bindtap="gotoCart">查看购物车</button>
      <button wx:if="{{cartDialogType === 'add'}}" class="cart-dialog-btn cart-dialog-btn-yellow" bindtap="closeCartDialog">继续浏览</button>
      <button wx:if="{{cartDialogType === 'cart'}}" class="cart-dialog-btn cart-dialog-btn-blue" bindtap="checkout">去结算</button>
    </view>
  </view>
</view> 